package cn.iocoder.yudao.module.train.controller.admin.data;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.*;
import cn.iocoder.yudao.module.train.model.data.req.StaffOnlineCourseReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOnlineCourseDetailReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffProjectStatisticReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffProjectDetailReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOfflineCourseReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOfflineClassReq;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOnlineCourseResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOnlineCourseDetailResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffProjectStatisticResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffProjectDetailResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOfflineCourseResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOfflineClassResp;
import cn.iocoder.yudao.module.train.service.data.IStaffCourseStatisticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;

/**
 * 员工线上课程统计 Controller
 */
@RestController
@RequestMapping("/train/data/staff-course")
@Tag(name = "数据 - 员工数据统计")
public class StaffStatisticController {

    @Resource
    private IStaffCourseStatisticService staffCourseStatisticService;


    // TODO 员工数据概览

    @GetMapping("/staff/complex-statistics")
    @Operation(summary = "员工数据明细")
    public CommonResult<PageResult<StaffComplexStatisticResp>> getStaffComplexStatistics(@Valid StaffComplexStatisticReq req) {
        return  CommonResult.success(staffCourseStatisticService.getStaffComplexStatistics(req));
    }

    @GetMapping("/page")
    @Operation(summary = "获取员工线上课程统计分页")
    public CommonResult<PageResult<StaffOnlineCourseResp>> getStaffOnlineCoursePage(@Valid StaffOnlineCourseReq pageReq) {
        return CommonResult.success(staffCourseStatisticService.getStaffOnlineCoursePage(pageReq));
    }


    @GetMapping("/detail/page")
    @Operation(summary = "获取员工线上课程明细分页")
    public CommonResult<PageResult<StaffOnlineCourseDetailResp>> getStaffOnlineCourseDetailPage(@Valid StaffOnlineCourseDetailReq pageReq) {
        return CommonResult.success(staffCourseStatisticService.getStaffOnlineCourseDetailPage(pageReq));
    }


    @GetMapping("/project/page")
    @Operation(summary = "获取员工培训项目统计分页")
    public CommonResult<PageResult<StaffProjectStatisticResp>> getStaffProjectStatisticPage(@Valid StaffProjectStatisticReq pageReq) {
        return CommonResult.success(staffCourseStatisticService.getStaffProjectStatisticPage(pageReq));
    }

    @GetMapping("/project/detail/page")
    @Operation(summary = "获取员工培训项目明细分页")
    public CommonResult<PageResult<StaffProjectDetailResp>> getStaffProjectDetailPage(@Valid StaffProjectDetailReq pageReq) {
        return CommonResult.success(staffCourseStatisticService.getStaffProjectDetailPage(pageReq));
    }

    @GetMapping("/offline/page")
    @Operation(summary = "获取员工线下课程明细分页")
    public CommonResult<PageResult<StaffOfflineCourseResp>> getStaffOfflineCoursePage(@Valid StaffOfflineCourseReq pageReq) {
        return CommonResult.success(staffCourseStatisticService.getStaffOfflineCoursePage(pageReq));
    }


    @GetMapping("/offline/class/page")
    @Operation(summary = "获取员工线下课程开班明细分页")
    public CommonResult<PageResult<StaffOfflineClassResp>> getStaffOfflineClassPage(@Valid StaffOfflineClassReq pageReq) {
        return CommonResult.success(staffCourseStatisticService.getStaffOfflineClassPage(pageReq));
    }

    @GetMapping("/study-time/statistics")
    @Operation(summary = "根据时间、部门、岗位统计用户课程学习时长")
    public CommonResult<UserCourseStudyTimeStatisticResp> getUserCourseStudyTimeStatistics(
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime,
            @RequestParam(value = "deptId", required = false) Long deptId,
            @RequestParam(value = "postId", required = false) Long postId) {
        return CommonResult.success(staffCourseStatisticService.getUserCourseStudyTimeStatistics(startTime, endTime, deptId, postId));
    }

    @GetMapping("/knowledge/behavior/statistics")
    @Operation(summary = "员工知识库行为统计")
    public CommonResult<StaffKnowledgeBehaviorCountStatistic> getStaffKnowledgeBehaviorStatistics(
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime,
            @RequestParam(value = "deptId", required = false) Long deptId,
            @RequestParam(value = "postId", required = false) Long postId) {
        return CommonResult.success(staffCourseStatisticService.getStaffKnowledgeBehaviorCountStatistics(
                startTime, endTime, deptId, postId));
    }

    @GetMapping("/dept/question-answer/statistics")
    @Operation(summary = "查询部门员工总提问和讨论数")
    public CommonResult<DeptQuestionAnswerStatisticResp> getDeptQuestionAnswerStatistics(
            @RequestParam(value = "deptId", required = false) Long deptId,
            @RequestParam(value = "postId", required = false) Long postId,
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime
    ) {

        return CommonResult.success(staffCourseStatisticService.getDeptQuestionAnswerStatistics(deptId, startTime, endTime, postId));
    }

    @GetMapping("/topic/statistics")
    @Operation(summary = "员工话题/回复/资讯评论统计")
    public CommonResult<StaffTopicStatisticSummaryResp> getStaffTopicStatistics(@RequestParam(value = "deptId", required = false) Long deptId,
                                                                                @RequestParam(value = "postId", required = false) Long postId,
                                                                                @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
                                                                                @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime) {
        return CommonResult.success(staffCourseStatisticService.getStaffTopicStatisticSummary(
                deptId, postId, startTime, endTime));
    }
    @GetMapping("/interaction/statistics")
    @Operation(summary = "获取交流与互动统计数据")
    public CommonResult<StaffInteractionStatisticRespVO> getStaffInteractionStatistics(
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime,
            @RequestParam(value = "deptId", required = false) Long deptId,
            @RequestParam(value = "postId", required = false) Long postId) {
        return CommonResult.success(staffCourseStatisticService.getStaffInteractionStatistics(
                startTime, endTime, deptId, postId));
    }
    @GetMapping("/learning-participation/statistics")
    @Operation(summary = "获取学习参与度统计数据")
    public CommonResult<StaffLearningParticipationRespVO> getStaffLearningParticipationStatistics(
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime,
            @RequestParam(value = "deptId", required = false) Long deptId,
            @RequestParam(value = "postId", required = false) Long postId,
            @RequestParam(value = "roleId", required = false) Long roleId,
            @RequestParam(value = "employDaysGreaterThan", required = false) Integer employDaysGreaterThan) {
        return CommonResult.success(staffCourseStatisticService.getStaffLearningParticipationStatistics(
                startTime, endTime, deptId, postId, roleId, employDaysGreaterThan));
    }

} 