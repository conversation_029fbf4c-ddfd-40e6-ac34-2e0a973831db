<!--
 * @Author: 陈紫湘 <EMAIL>
 * @Date: 2025-04-02 10:53:04
 * @LastEditors: 陈紫湘 <EMAIL>
 * @LastEditTime: 2025-04-02 15:08:57
 * @FilePath: \edu-train-web-pc\src\views\data\trainData\CourseProject\components\CourseDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="overview-container">
    <!-- 原始专题列表界面 -->
    <div v-if="!showParticipantsView">
      <!-- 搜索区域 -->
      <div class="detail-search">
        <el-form :model="detailQueryParams" inline>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DDTHH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              style="width: 360px"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item label="部门">
            <DeptTreeSelectName v-model="detailQueryParams.deptName" />
          </el-form-item>
          <el-form-item label="专题分类">
            <CourseCategorySelect
              v-model="detailQueryParams.categoryId"
              style="width: 200px"
              type="topic"
            />
          </el-form-item>
          <el-form-item label="标题">
            <el-input clearable v-model="detailQueryParams.name" placeholder="请输入标题" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button>导出数据</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <div class="detail-search">
        <el-table
          :data="filteredTableData"
          style="width: 100%"
          :header-cell-style="{ background: '#F5F5F5' }"
        >
          <el-table-column prop="name" label="专题名称" fixed="left" min-width="300">
            <template #default="{ row }">
              <div class="topic-title-cell">
                <el-image
                  :src="row.coverImageUrl"
                  class="topic-image"
                  fit="cover"
                  :preview-src-list="[row.coverImageUrl]"
                  :initial-index="0"
                  :preview-teleported="true"
                  :hide-on-click-modal="false"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <span class="topic-title">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="120">
            <template #default="{ row }">
              {{ formatDateString(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="所属部门" min-width="200" show-overflow-tooltip />
          <el-table-column prop="categoryName" label="专题分类" min-width="100" />
          <el-table-column prop="userCount" label="专题人数" min-width="100" />
          <el-table-column prop="totalLearnTimeMinutes" label="学习总时长" min-width="100" />
          <el-table-column prop="avgLearnTimeMinutes" label="人均学习时长" min-width="120" />
          <el-table-column fixed="right" label="操作" min-width="180">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleViewDetail(row)">查看详情</el-button>
              <el-button type="primary" link @click="handleDownload(row)">下载数据</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="page.pageNum"
          v-model:page-size="page.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 专题参与者详情页面 -->
    <div v-if="showParticipantsView" class="topic-participants-view">
      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <span class="topic-title">{{ currentTopic?.name }}</span>
        <el-button link type="primary" @click="backToTopicList">返回</el-button>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <span class="label">学员姓名</span>
            <el-input
              v-model="participantQuery.nickname"
              placeholder="请输入学员姓名"
              style="width: 180px"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-item">
            <el-button type="primary" @click="searchParticipants">查询</el-button>
          </div>
          <div class="filter-item">
            <el-button @click="exportParticipants">导出结果</el-button>
          </div>
        </div>
      </div>

      <!-- 参与者数据表格 -->
      <el-table
        v-loading="participantsLoading"
        :data="participantsList"
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column label="姓名" width="200">
          <template #default="{ row }">
            <span>{{ row.nickname }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属部门/岗位" width="200">
          <template #default="{ row }">
            <div>
              <div>{{ row.deptName }}</div>
              <div v-if="row.postName" class="position">{{ row.postName }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="加入时间" width="180" align="center">
          <template #default="{ row }">
            {{ row.joinTime ? formatDateString(row.joinTime) : '--' }}
          </template>
        </el-table-column>

        <el-table-column label="完成任务数" width="120" align="center">
          <template #default="{ row }">
            {{ row.completedTaskCount }}
          </template>
        </el-table-column>

        <el-table-column label="完成进度" width="120" align="center">
          <template #default="{ row }">
            {{ (row.completionRate || 0) + '%' }}
          </template>
        </el-table-column>

        <el-table-column label="累计学习时长(分钟)" width="150" align="center">
          <template #default="{ row }">
            {{ row.learnDuration }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="participantsTotal"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="participantsPageSize"
          :current-page="participantsCurrentPage"
          @size-change="handleParticipantsSizeChange"
          @current-change="handleParticipantsCurrentChange"
        />
      </div>
    </div>

    <!-- 学员学习情况弹框 -->
    <el-dialog
      v-model="showStudentDetailDialog"
      :title="`${currentStudent?.name || '学员'}的学习情况`"
      width="800px"
      :before-close="handleCloseStudentDetail"
    >
      <div class="student-detail-content">
        <!-- 成绩单部分 -->
        <div class="transcript-section">
          <div class="section-header">
            <div class="blue-line"></div>
            <span class="section-title">成绩单</span>
            <div class="summary-info">
              作业:{{ currentStudent?.homeworkCount || 0 }}/{{
                currentStudent?.homeworkTotal || 0
              }}
              考试:{{ currentStudent?.examCount || 0 }}/{{ currentStudent?.examTotal || 0 }}
            </div>
          </div>

          <!-- 考试列表 -->
          <div v-if="currentStudent?.exams && currentStudent.exams.length > 0" class="exam-list">
            <div v-for="(exam, index) in currentStudent.exams" :key="index" class="exam-item">
              <div class="exam-header">
                <el-icon class="exam-icon"><Document /></el-icon>
                <span class="exam-name">考试: {{ exam.name }}</span>
                <span class="exam-score" :class="{ failed: exam.score < exam.totalScore * 0.6 }">
                  {{ exam.score }}分/{{ exam.totalScore }}分
                </span>
              </div>
              <div class="exam-details">
                <span class="exam-attempt">第{{ exam.attempt }}次考试 {{ exam.date }}</span>
                <span class="exam-divider">—</span>
                <span class="exam-score-display">{{ exam.score }}</span>
                <span class="exam-comment">评语</span>
              </div>
            </div>
          </div>

          <!-- 作业列表 -->
          <div
            v-if="currentStudent?.homeworks && currentStudent.homeworks.length > 0"
            class="homework-list"
          >
            <div
              v-for="(homework, index) in currentStudent.homeworks"
              :key="index"
              class="homework-item"
            >
              <div class="homework-header">
                <el-icon class="homework-icon"><Document /></el-icon>
                <span class="homework-name">作业: {{ homework.name }}</span>
                <span
                  class="homework-score"
                  :class="{ failed: homework.score < homework.totalScore * 0.6 }"
                >
                  {{ homework.score }}分/{{ homework.totalScore }}分
                </span>
              </div>
              <div class="homework-details">
                <span class="homework-date">{{ homework.date }}</span>
                <span class="homework-divider">—</span>
                <span class="homework-score-display">{{ homework.score }}</span>
                <span class="homework-comment">评语</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 学习数据部分 -->
        <div class="learning-data-section">
          <div class="section-header">
            <div class="blue-line"></div>
            <span class="section-title">学习数据</span>
          </div>

          <div class="learning-stats">
            <div class="stat-card">
              <div class="stat-number">{{ currentStudent?.questionCount || 0 }}</div>
              <div class="stat-label">发起提问</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ currentStudent?.activityCount || 0 }}</div>
              <div class="stat-label">参加活动</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ currentStudent?.topicCount || 0 }}</div>
              <div class="stat-label">发起话题</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ currentStudent?.replyCount || 0 }}</div>
              <div class="stat-label">回复话题</div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <span class="update-time">{{ currentStudent?.updateTime || '2025-06-09 14:50:48' }}</span>
          <el-button @click="handleCloseStudentDetail">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CourseDataApi } from '@/api/data/train/course/index'
import { formatDate } from '@/utils/formatTime'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// 定义表格数据的类型
interface TableItem {
  id: number
  name: string
  createTime: string
  deptName: string
  categoryName: string
  userCount: number
  totalLearnTimeMinutes: number
  avgLearnTimeMinutes: number
  coverImageUrl: string
}

const router = useRouter()

// 明细查询参数
const detailQueryParams = ref({
  startTime: '',
  endTime: '',
  deptName: undefined as unknown as string,
  categoryId: undefined as unknown as number,
  name: undefined as unknown as string
})

const page = ref({
  pageNum: 1,
  pageSize: 10
})
const total = ref(0)
// 表格数据
const tableData = ref<TableItem[]>([])
// 过滤后的数据
const filteredTableData = ref<TableItem[]>([])

// 专题参与者详情页面相关
const showParticipantsView = ref(false)
const currentTopic: any = ref(null)
const participantQuery: any = ref({})
const participantsList: any = ref([])
const participantsLoading = ref(false)
const participantsTotal = ref(0)
const participantsPageSize = ref(10)
const participantsCurrentPage = ref(1)

// 学员学习情况弹框相关
const showStudentDetailDialog = ref(false)
const currentStudent: any = ref(null)

// 日期范围选择器
const dateRange = ref<[string, string] | undefined>(undefined)

// 格式化日期
const formatDateString = (dateString: string | undefined): string => {
  if (!dateString) return '--'
  try {
    return formatDate(new Date(dateString))
  } catch (error) {
    return '--'
  }
}

// 日期范围变化时更新查询参数
const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    detailQueryParams.value.startTime = dateRange.value[0]
    detailQueryParams.value.endTime = dateRange.value[1]
  } else {
    detailQueryParams.value.startTime = ''
    detailQueryParams.value.endTime = ''
  }
  page.value.pageNum = 1 // 重置到第一页
  getList()
}

// 查询
const handleQuery = async () => {
  page.value.pageNum = 1 // 重置到第一页
  await getList()
}

// 重置
const handleReset = () => {
  dateRange.value = undefined
  detailQueryParams.value = {
    startTime: '',
    endTime: '',
    deptName: undefined as unknown as string,
    categoryId: undefined as unknown as number,
    name: undefined as unknown as string
  }
  page.value.pageNum = 1
  getList()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  page.value.pageSize = size
  page.value.pageNum = 1 // 切换每页条数时重置为第一页
  getList()
}

// 页码变化
const handleCurrentChange = (num: number) => {
  page.value.pageNum = num
  getList()
}

const getList = async () => {
  const params = {
    ...detailQueryParams.value,
    ...page.value
  }
  const data = await CourseDataApi.getSpecialDetail(params)
  tableData.value = data.list
  total.value = data.total

  // 根据日期范围过滤数据
  if (detailQueryParams.value.startTime || detailQueryParams.value.endTime) {
    filteredTableData.value = tableData.value.filter((item) => {
      const createTime = new Date(item.createTime)
      const startTime = detailQueryParams.value.startTime
        ? new Date(detailQueryParams.value.startTime)
        : null
      const endTime = detailQueryParams.value.endTime
        ? new Date(detailQueryParams.value.endTime)
        : null

      if (startTime && endTime) {
        return createTime >= startTime && createTime <= endTime
      } else if (startTime) {
        return createTime >= startTime
      } else if (endTime) {
        return createTime <= endTime
      }
      return true
    })
  } else {
    filteredTableData.value = tableData.value
  }
}

// 下载数据
const handleDownload = (row: any) => {
  console.log('下载数据:', row.name)
}

// 查看详情
const handleViewDetail = (row: any) => {
  currentTopic.value = row
  showParticipantsView.value = true
  participantQuery.value = {
    courseId: row.id, // 只传课程ID
    nickname: '' // 学员姓名，默认空
  }
  getParticipantsList()
}

// 返回专题列表
const backToTopicList = () => {
  showParticipantsView.value = false
  currentTopic.value = null
  participantsList.value = []
}

// 专题参与者详情页面相关方法
const searchParticipants = async () => {
  participantsCurrentPage.value = 1
  await getParticipantsList()
}

const getParticipantsList = async () => {
  participantsLoading.value = true
  try {
    // 只传递后端需要的参数
    const params = {
      courseId: participantQuery.value.courseId,
      nickname: participantQuery.value.nickname || '',
      pageNum: participantsCurrentPage.value,
      pageSize: participantsPageSize.value
    }
    const data = await CourseDataApi.getSpecialCourseStudentDetail(params)
    participantsList.value = data.list || []
    participantsTotal.value = data.total || 0
  } catch (error) {
    console.error('获取专题参与者数据失败:', error)
    ElMessage.error('获取数据失败')
    participantsList.value = []
    participantsTotal.value = 0
  } finally {
    participantsLoading.value = false
  }
}

const handleParticipantsSizeChange = (size: number) => {
  participantsPageSize.value = size
  participantsCurrentPage.value = 1
  getParticipantsList()
}

const handleParticipantsCurrentChange = (num: number) => {
  participantsCurrentPage.value = num
  getParticipantsList()
}

const exportParticipants = () => {
  // 只导出当前筛选条件下的专题课程学员明细
  const params = {
    courseId: participantQuery.value.courseId,
    nickname: participantQuery.value.nickname || ''
  }
  CourseDataApi.exportSpecialCourseStudentDetail(params)
}

const viewParticipantDetail = (row: any) => {
  console.log('查看参与者详情:', row)
  // 模拟获取学员学习情况
  currentStudent.value = {
    name: row.name,
    alias: row.alias,
    department: row.department,
    position: row.position,
    joinTime: row.joinTime,
    completeTime: row.completeTime,
    totalStudyTime: row.totalStudyTime,
    completionProgress: row.completionProgress,
    updateTime: '2025-06-09 14:50:48',
    homeworkCount: 5,
    homeworkTotal: 10,
    examCount: 3,
    examTotal: 5,
    exams: [
      { name: '期中考试', score: 85, totalScore: 100, attempt: 1, date: '2025-05-10' },
      { name: '期末考试', score: 92, totalScore: 100, attempt: 2, date: '2025-06-15' }
    ],
    homeworks: [
      { name: '第一周作业', score: 90, totalScore: 100, date: '2025-04-20' },
      { name: '第二周作业', score: 88, totalScore: 100, date: '2025-04-27' },
      { name: '第三周作业', score: 95, totalScore: 100, date: '2025-05-04' },
      { name: '第四周作业', score: 87, totalScore: 100, date: '2025-05-11' },
      { name: '第五周作业', score: 92, totalScore: 100, date: '2025-05-18' }
    ],
    questionCount: 15,
    activityCount: 20,
    topicCount: 10,
    replyCount: 30
  }
  showStudentDetailDialog.value = true
}

const handleCloseStudentDetail = () => {
  showStudentDetailDialog.value = false
  currentStudent.value = null
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.overview-container {
  padding: 20px;
  background-color: #f5f5f5;
}
.detail-search {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
}
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.topic-title-cell {
  display: flex;
  align-items: center;
  gap: 12px;

  .topic-image {
    width: 100px;
    height: 60px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
  }

  .image-error {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    color: #909399;
    font-size: 20px;
  }

  .topic-title {
    font-size: 14px;
    color: #303133;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

/* 专题参与者详情页面样式 */
.topic-participants-view {
  background-color: #f5f5f5;

  .breadcrumb {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 15px 20px;

    .topic-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }

  .filter-section {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 20px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }

    .filter-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 14px;
        color: #606266;
        margin-right: 10px;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
}

/* 学员学习情况弹框样式 */
.student-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .transcript-section {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 20px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .blue-line {
        width: 4px;
        height: 100%;
        background-color: #409eff;
        margin-right: 10px;
      }

      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .summary-info {
        margin-left: 20px;
        font-size: 14px;
        color: #909399;
      }
    }

    .exam-list,
    .homework-list {
      .exam-item,
      .homework-item {
        padding: 15px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .exam-header,
        .homework-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 5px;

          .exam-name,
          .homework-name {
            font-size: 15px;
            font-weight: bold;
            color: #303133;
            flex-grow: 1;
            margin-right: 10px;
          }

          .exam-score,
          .homework-score {
            font-size: 14px;
            color: #67c23a;
            font-weight: bold;

            &.failed {
              color: #f56c6c;
            }
          }
        }

        .exam-details,
        .homework-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 13px;
          color: #909399;

          .exam-attempt,
          .homework-date {
            flex-shrink: 0;
          }

          .exam-divider,
          .homework-divider {
            margin: 0 5px;
          }

          .exam-score-display,
          .homework-score-display {
            font-weight: bold;
            color: #303133;
          }

          .exam-comment,
          .homework-comment {
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .learning-data-section {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 20px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .blue-line {
        width: 4px;
        height: 100%;
        background-color: #409eff;
        margin-right: 10px;
      }

      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }

    .learning-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;

      .stat-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;

        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 13px;
          color: #909399;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #909399;

    .update-time {
      margin-right: 20px;
    }
  }
}
</style>
