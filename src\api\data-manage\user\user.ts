import request from '@/config/axios'

// 员工培训数据查询参数接口
export interface StaffCourseQuery {
  pageNo: number // 页码，从 1 开始
  pageSize: number // 每页条数，最大值为 100
  beginTime?: string // 学习时间区间-开始
  endTime?: string // 学习时间区间-结束
  deptId?: string | number // 部门编号
  keyword?: string // 员工工号或名称
  courseName?: string // 课程名称
  courseType?: string // 课程类型
  postId?: string | number // 岗位编号
  userId?: string | number // 用户编号
  name?: string // 姓名关键字
  categoryId?: string | number // 课程分类ID
}

// 培训计划查询参数接口
export interface TrainingPlanQuery {
  pageNo: number // 页码，从 1 开始
  pageSize: number // 每页条数，最大值为 100
  startTime?: string // 计划时间-开始
  endTime?: string // 计划时间-结束
  department?: string // 所属部门
  departmentId?: string // 所属部门ID
  planType?: string // 计划类型
}

// 培训项目明细查询参数接口
export interface TrainingProjectDetailQuery {
  pageNo: number // 页码，从 1 开始
  pageSize: number // 每页条数，最大值为 100
  userName?: string // 姓名
  projectId?: string // 项目ID
  projectName?: string // 项目名称
  projectCategory?: string // 项目分类
  departmentId?: string // 所属部门
  position?: string // 员工职位
  startJoinTime?: string // 参与开始时间
  endJoinTime?: string // 参与结束时间
  completionStatus?: string // 完成状态(0未完成，1已完成，2进行中)
}

// 用户培训数据API
export const UserTrainingApi = {
  // 获取员工培训项目统计分页
  getStaffProjectPage: (params: StaffCourseQuery) => {
    return request.Get({ url: '/train/data/staff-course/project/page', params })
  },

  // 获取员工培训项目明细分页
  getStaffProjectDetailPage: (params: StaffCourseQuery) => {
    return request.Get({ url: '/train/data/staff-course/project/detail/page', params })
  },

  // 获取员工线上课程统计分页
  getStaffCoursePage: (params: StaffCourseQuery) => {
    return request.Get({ url: '/train/data/staff-course/page', params })
  },

  // 获取员工线下课程明细分页
  getStaffOfflineCoursePage: (params: StaffCourseQuery) => {
    return request.Get({ url: '/train/data/staff-course/offline/page', params })
  },

  // 获取员工线下课程开班明细分页
  getStaffOfflineClassPage: (params: StaffCourseQuery) => {
    return request.Get({ url: '/train/data/staff-course/offline/class/page', params })
  },

  // 获取员工线上课程明细分页
  getStaffCourseDetailPage: (params: StaffCourseQuery) => {
    return request.Get({ url: '/train/data/staff-course/detail/page', params })
  },

  // 导出员工培训项目统计
  exportStaffProject: (params: Partial<StaffCourseQuery>) => {
    return request.download({
      url: '/train/data/staff-course-export/project/export',
      params
    })
  },

  // 导出员工培训项目明细
  exportStaffProjectDetail: (params: Partial<StaffCourseQuery>) => {
    return request.download({
      url: '/train/data/staff-course-export/project/detail/export',
      params
    })
  },

  // 导出员工线上课程统计
  exportStaffCourse: (params: Partial<StaffCourseQuery>) => {
    return request.download({
      url: '/train/data/staff-course-export/export',
      params
    })
  },

  // 导出员工线下课程明细
  exportStaffOfflineCourse: (params: Partial<StaffCourseQuery>) => {
    return request.download({
      url: '/train/data/staff-course-export/offline/export',
      params
    })
  },

  // 导出员工线下课程开班明细
  exportStaffOfflineClass: (params: Partial<StaffCourseQuery>) => {
    return request.download({
      url: '/train/data/staff-course-export/offline/class/export',
      params
    })
  },

  // 导出员工线上课程明细
  exportStaffCourseDetail: (params: Partial<StaffCourseQuery>) => {
    return request.download({
      url: '/train/data/staff-course-export/detail/export',
      params
    })
  },

  // 导出培训项目明细
  exportTrainingProjectDetail: (params: any) => {
    return request.download({
      url: '/train/data/project-detail/export',
      params
    })
  },

  // 分页查询培训计划数据
  getTrainingPlanPage: (params: TrainingPlanQuery) => {
    return request.Get({ url: '/train/data/plan/pageQuery', params })
  },

  // 分页查询培训项目明细数据
  getTrainingProjectDetailPage: (params: TrainingProjectDetailQuery) => {
    return request.Get({ url: '/train/data/project-detail/page', params })
  },

  // 统计员工学习时长
  getStaffStudyTimeStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/study-time/statistics', params })
  },

  // 员工话题/回复/资讯评论统计
  getStaffTopicStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/topic/statistics', params })
  },

  // 统计员工知识库行为统计
  getStaffKnowledgeBehaviorStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/study-time/knowledge/behavior/statistics', params })
  },

  // 统计员工问答次数
  getStaffQuestionAnswerStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/dept/question-answer/statistics', params })
  },

  // 员工数据明细
  getStaffComplexStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/staff/complex-statistics', params })
  },

  // 导出员工数据明细
  exportStaffComplexStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.download({ url: '/train/data/staff-course-export/staff/complex-statistics/export', params })
  },

  // 获取交流与互动统计数据
  getStaffInteractionStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/interaction/statistics', params })
  },

  // 获取学习参与度统计数据
  getStaffLearningParticipationStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/learning-participation/statistics', params })
  },

  // 根据时间、部门、岗位统计用户课程学习时长
  getStaffStudyTimeStatisticsByCondition: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/study-time/statistics', params })
  },

  // 员工知识库行为统计
  getStaffKnowledgeBehaviorStatisticsByCondition: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/knowledge/behavior/statistics', params })
  },

  // 查询部门员工总提问和讨论数
  getStaffDeptQuestionAnswerStatistics: (params: Partial<StaffCourseQuery>) => {
    return request.Get({ url: '/train/data/staff-course/dept/question-answer/statistics', params })
  }
}
