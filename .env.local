# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径
#VITE_BASE_URL='https://edu.hnqhkeji.com/api'
# VITE_BASE_URL='http://w45578a6.natappfree.cc'
VITE_BASE_URL='http://localhost:48080'
# VITE_BASE_URL='http://**************:48080'
# VITE_BASE_URL='http://***************:48080'
# VITE_BASE_URL='http://*************:48080'

# VITE_BASE_URL='http://**************:48080'
# VITE_BASE_URL='http://**************/eduadmin-api'再
# VITE_BASE_URL='http://*************:48080'


# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=client

# 分片上传配置
# 是否启用分片上传
VITE_ENABLE_MULTIPART_UPLOAD=true

# 分片上传阈值（字节），大于此值的文件使用分片上传
# 50MB = 50 * 1024 * 1024 = 52428800
VITE_MULTIPART_UPLOAD_THRESHOLD=52428800

# 默认分片大小（字节）
# 20MB = 20 * 1024 * 1024 = 20971520
VITE_MULTIPART_PART_SIZE=20971520

# 并发上传分片数
VITE_MULTIPART_PARALLEL=5

# 接口地址

VITE_API_URL=/admin-api


# 用户端接口地址
VITE_USER_API_URL=/app-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'



