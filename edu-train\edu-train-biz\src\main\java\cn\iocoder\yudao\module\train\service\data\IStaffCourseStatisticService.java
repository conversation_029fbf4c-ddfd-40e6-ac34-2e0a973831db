package cn.iocoder.yudao.module.train.service.data;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.*;
import cn.iocoder.yudao.module.train.model.data.req.StaffOnlineCourseReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOnlineCourseDetailReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffProjectStatisticReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffProjectDetailReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOfflineCourseReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOfflineClassReq;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOnlineCourseResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOnlineCourseDetailResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffProjectStatisticResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffProjectDetailResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOfflineCourseResp;
import cn.iocoder.yudao.module.train.model.data.resp.StaffOfflineClassResp;

import java.time.LocalDate;
import java.util.List;

/**
 * 员工课程统计服务接口
 */
public interface IStaffCourseStatisticService {

    /**
     * 获取员工线上课程统计分页
     *
     * @param pageReq 分页请求参数
     * @return 统计分页结果
     */
    PageResult<StaffOnlineCourseResp> getStaffOnlineCoursePage(StaffOnlineCourseReq pageReq);

    /**
     * 获取员工学习明细统计分页
     *
     * @param pageReq 分页请求参数
     * @return 学习明细分页结果
     */
    PageResult<StaffOnlineCourseDetailResp> getStaffOnlineCourseDetailPage(StaffOnlineCourseDetailReq pageReq);

    /**
     * 导出员工线上课程统计数据
     *
     * @param exportReq 导出请求参数
     * @return 统计数据
     */
    List<StaffOnlineCourseResp> exportStaffOnlineCourseList(StaffOnlineCourseReq exportReq);

    /**
     * 导出员工学习明细统计数据
     *
     * @param exportReq 导出请求参数
     * @return 统计数据
     */
    List<StaffOnlineCourseDetailResp> exportStaffOnlineCourseDetailList(StaffOnlineCourseDetailReq exportReq);
    
    /**
     * 获取员工培训项目统计分页
     *
     * @param pageReq 分页请求参数
     * @return 统计分页结果
     */
    PageResult<StaffProjectStatisticResp> getStaffProjectStatisticPage(StaffProjectStatisticReq pageReq);
    
    /**
     * 导出员工培训项目统计数据
     *
     * @param exportReq 导出请求参数
     * @return 统计数据
     */
    List<StaffProjectStatisticResp> exportStaffProjectStatisticList(StaffProjectStatisticReq exportReq);
    
    /**
     * 获取员工培训项目明细分页
     *
     * @param pageReq 分页请求参数
     * @return 明细分页结果
     */
    PageResult<StaffProjectDetailResp> getStaffProjectDetailPage(StaffProjectDetailReq pageReq);
    
    /**
     * 导出员工培训项目明细数据
     *
     * @param exportReq 导出请求参数
     * @return 明细数据
     */
    List<StaffProjectDetailResp> exportStaffProjectDetailList(StaffProjectDetailReq exportReq);

    /**
     * 获取员工线下课程明细分页
     *
     * @param pageReq 分页请求参数
     * @return 明细分页结果
     */
    PageResult<StaffOfflineCourseResp> getStaffOfflineCoursePage(StaffOfflineCourseReq pageReq);
    
    /**
     * 导出员工线下课程明细数据
     *
     * @param exportReq 导出请求参数
     * @return 明细数据
     */
    List<StaffOfflineCourseResp> exportStaffOfflineCourseList(StaffOfflineCourseReq exportReq);

    /**
     * 获取员工线下课程开班明细分页
     *
     * @param pageReq 分页请求参数
     * @return 明细分页结果
     */
    PageResult<StaffOfflineClassResp> getStaffOfflineClassPage(StaffOfflineClassReq pageReq);
    
    /**
     * 导出员工线下课程开班明细数据
     *
     * @param exportReq 导出请求参数
     * @return 明细数据
     */
    List<StaffOfflineClassResp> exportStaffOfflineClassList(StaffOfflineClassReq exportReq);

    UserCourseStudyTimeStatisticResp getUserCourseStudyTimeStatistics(LocalDate startTime, LocalDate endTime, Long deptId, Long postId);

    List<StaffKnowledgeBehaviorStatisticResp> getStaffKnowledgeBehaviorStatistics(LocalDate startTime, LocalDate endTime, Long deptId, Long postId);

    DeptQuestionAnswerStatisticResp getDeptQuestionAnswerStatistics(Long deptId, LocalDate startTime, LocalDate endTime, Long postId);

    List<StaffTopicStatisticResp> getStaffTopicStatistics(Long deptId, LocalDate startTime, LocalDate endTime, Long postId);

    PageResult<StaffComplexStatisticResp> getStaffComplexStatistics(StaffComplexStatisticReq req);

    List<StaffComplexStatisticResp> exportStaffComplexStatisticsList(StaffComplexStatisticReq req);

    /**
     * 获取员工知识库行为统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param postId 岗位ID
     * @return 知识库行为统计
     */
    StaffKnowledgeBehaviorCountStatistic getStaffKnowledgeBehaviorCountStatistics(
            LocalDate startTime, LocalDate endTime, Long deptId, Long postId);

    /**
     * 获取员工话题统计汇总
     *
     * @param deptId 部门ID
     * @param postId 岗位ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 话题统计汇总
     */
    StaffTopicStatisticSummaryResp getStaffTopicStatisticSummary(
            Long deptId, Long postId, LocalDate startTime, LocalDate endTime);

    StaffInteractionStatisticRespVO getStaffInteractionStatistics(LocalDate startTime, LocalDate endTime, Long deptId, Long postId);

    StaffLearningParticipationRespVO getStaffLearningParticipationStatistics(LocalDate startTime, LocalDate endTime, Long deptId, Long postId, Long roleId, Integer employDaysGreaterThan);

}