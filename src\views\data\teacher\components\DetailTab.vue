<template>
  <div class="teacher-detail-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <span class="label">时间范围</span>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DDTHH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 360px"
            @change="handleDateChange"
          />
        </div>
        <div class="filter-item">
          <span class="label">部门筛选</span>
          <dept-tree-select
            v-model="queryParams.departmentId"
            placeholder="请选择部门"
            style="width: 240px"
            clearable
          />
        </div>
        <div class="filter-item">
          <span class="label">岗位筛选</span>
          <post-tree-select v-model="queryParams.positionId" placeholder="选择岗位" clearable />
        </div>
        <div class="filter-item">
          <span class="label">模糊查询</span>
          <el-select v-model="queryParams.searchField" placeholder="姓名" style="width: 120px">
            <el-option label="姓名" value="name" />
          </el-select>
          <el-input
            v-model="queryParams.keyword"
            placeholder="关键词"
            clearable
            @keyup.enter="handleSearch"
            style="width: 180px; margin-left: 8px"
          />
        </div>
      </div>
      <div class="filter-row">
        <div class="filter-item">
          <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
          <el-button @click="handleReset" style="margin-left: 10px">重置</el-button>
          <el-button type="primary" plain style="margin-left: 10px" @click="handleExport">
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="teacherList"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      row-key="id"
    >
      <el-table-column label="讲师名" min-width="150">
        <template #default="{ row }">
          <div class="user-info">
            <el-avatar :size="40" :src="row.avatar" class="user-avatar">
              {{ row.name ? row.name.substr(0, 1) : '?' }}
            </el-avatar>
            <div class="user-name">{{ row.name || '--' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属部门" prop="departmentName" min-width="180" />
      <el-table-column label="岗位" prop="positionName" min-width="120" />
      <el-table-column label="等级" prop="level" min-width="100" align="center" />
      <el-table-column label="领域" min-width="120">
        <template #default="{ row }">
          <div class="domain-info">
            <span>{{ row.specialty || '--' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="线上课程授课数"
        prop="onlineCourseCount"
        min-width="120"
        align="center"
      />
      <el-table-column label="线上课程总时长(h)" min-width="130" align="center">
        <template #default="{ row }">{{ row.onlineCourseDuration || 0 }}</template>
      </el-table-column>
      <el-table-column
        label="线上教学人次"
        prop="onlineParticipants"
        min-width="120"
        align="center"
      />
      <el-table-column label="线上课程评价" min-width="120" align="center">
        <template #default="{ row }">{{
          row.onlineEvaluation ? row.onlineEvaluation.toFixed(2) + '/5.00' : '暂无评价'
        }}</template>
      </el-table-column>
      <el-table-column
        label="线下课程授课数"
        prop="offlineCourseCount"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="线下授课学时(h)"
        prop="offlineTeachingHours"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="线下教学人次"
        prop="offlineParticipants"
        min-width="120"
        align="center"
      />
      <el-table-column label="线下课程评价" min-width="120" align="center">
        <template #default="{ row }">{{
          row.offlineEvaluation ? row.offlineEvaluation.toFixed(2) + '/5.00' : '暂无评价'
        }}</template>
      </el-table-column>
      <el-table-column
        label="期内教学奖励积分"
        prop="periodRewardPoints"
        min-width="130"
        align="center"
      />
      <el-table-column
        label="累计教学奖励积分"
        prop="totalRewardPoints"
        min-width="130"
        align="center"
      />
      <el-table-column label="操作" width="160" fixed="right">
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button link type="primary" size="small" @click="openTeachingArchive(row)"
              >教学档案</el-button
            >
            <el-button link type="primary" size="small" @click="openUserDetail(row)"
              >个人信息</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户详情对话框 -->
    <user-detail-dialog
      v-if="userDetailVisible"
      v-model="userDetailVisible"
      :username="currentUsername"
    />

    <!-- 教学档案对话框 -->
    <teaching-archive-dialog
      v-if="teachingArchiveVisible"
      v-model="teachingArchiveVisible"
      :teacher-data="currentTeacher || undefined"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
import UserDetailDialog from '@/components/UserDetailDialog/index.vue'
import TeachingArchiveDialog from '@/views/data/teacher/components/TeachingArchiveDialog.vue'
import {
  getLecturerDetailList,
  exportLecturerDetailList,
  type LecturerStatsParams
} from '@/api/data-manage/teacher/teacher'
import { formatDate } from '@/utils/formatTime'

// 定义 API 响应数据结构类型
interface LecturerDetailRespVO {
  id: number
  name?: string
  avatar?: string
  departmentName?: string
  positionName?: string
  level?: string
  specialty?: string
  onlineCourseCount?: number
  onlineCourseDuration?: number
  onlineParticipants?: number
  onlineEvaluation?: number
  offlineCourseCount?: number
  offlineTeachingHours?: number
  offlineParticipants?: number
  offlineEvaluation?: number
  periodRewardPoints?: number
  totalRewardPoints?: number
  username?: string
}

// 日期范围
const dateRange = ref<[string, string] | undefined>(undefined)

// 查询参数 - 类型根据 API 和组件要求调整
const queryParams = reactive<
  Omit<LecturerStatsParams, 'keyword'> & {
    searchField: string
    keyword: string
    pageNo: number
    pageSize: number
    beginTime?: string
    endTime?: string
    departmentId?: number | number[] | null
    positionId?: string | number | null
  }
>({
  departmentId: null as number | number[] | null,
  positionId: null as string | number | null,
  searchField: 'name',
  keyword: '',
  beginTime: '',
  endTime: '',
  pageNo: 1,
  pageSize: 10
})

// 分页相关
const total = ref(0)
const loading = ref(false)

// 表格数据
const teacherList = ref<LecturerDetailRespVO[]>([])

// 用户详情对话框相关
const userDetailVisible = ref(false)
const currentUsername = ref('')

// 教学档案对话框相关
const teachingArchiveVisible = ref(false)
const currentTeacher = ref<LecturerDetailRespVO | null>(null)

// 打开用户详情对话框
const openUserDetail = (row: LecturerDetailRespVO) => {
  currentUsername.value = row.username || row.name || ''
  userDetailVisible.value = true
}

// 打开教学档案对话框
const openTeachingArchive = (row: LecturerDetailRespVO) => {
  currentTeacher.value = row
  teachingArchiveVisible.value = true
}

// 搜索功能 - 对接 API
const handleSearch = async () => {
  loading.value = true
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.beginTime = dateRange.value[0]
      queryParams.endTime = dateRange.value[1]
    }

    // 构造请求参数，包含时间参数
    const params: LecturerStatsParams = {
      departmentId: queryParams.departmentId,
      positionId: queryParams.positionId,
      name: queryParams.searchField === 'name' ? queryParams.keyword : undefined,
      beginTime: queryParams.beginTime,
      endTime: queryParams.endTime
    }

    const response = await getLecturerDetailList(params as any)
    if (response.data && response.data) {
      teacherList.value = response.data
      total.value = response.data.total ? response.data.total : teacherList.value.length
    } else {
      teacherList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取讲师列表失败:', error)
    ElMessage.error('查询失败，请稍后重试')
    teacherList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 导出数据 - 对接 API
const handleExport = async () => {
  try {
    const params: LecturerStatsParams = {
      departmentId: queryParams.departmentId,
      positionId: queryParams.positionId,
      name: queryParams.searchField === 'name' ? queryParams.keyword : undefined,
      beginTime: queryParams.beginTime,
      endTime: queryParams.endTime
    }

    ElMessage.info('正在导出数据...')
    await exportLecturerDetailList(params as any)
  } catch (error) {
    console.error('导出讲师列表失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 重置查询参数
const handleReset = () => {
  dateRange.value = undefined
  queryParams.departmentId = null as number | number[] | null
  queryParams.positionId = null as string | number | null
  queryParams.searchField = 'name'
  queryParams.keyword = ''
  queryParams.beginTime = ''
  queryParams.endTime = ''
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  handleSearch()
}

// 分页方法
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  queryParams.pageNo = 1
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  handleSearch()
}

// 日期选择器 change 事件
const handleDateChange = () => {
  handleSearch()
}

// 暴露 getList 方法
defineExpose({
  getList: handleSearch
})

onMounted(() => {
  // 设置默认日期范围 - 直接使用字符串拼接

  handleSearch()
})
</script>

<style lang="scss" scoped>
.teacher-detail-tab {
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
    background-color: #f5f7fa;
    padding: 16px;
    font-size: 14px;
    border-radius: 4px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 5px;

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;

        .label {
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .user-avatar {
      background-color: #dce2f1;
      margin-right: 8px;
      color: #5a8def;
      font-size: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .user-name {
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .user-code {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .domain-info {
    display: flex;
    align-items: center;
    gap: 5px;

    .domain-tag {
      padding: 0 5px;
      height: 20px;
      line-height: 20px;
    }
  }

  .operation-buttons {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 8px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

:deep(.el-table__header) {
  th {
    font-weight: 600;
  }
}

:deep(.el-button.is-link) {
  padding: 2px 0;
  height: auto;
  font-size: 13px;
}

:deep(.dept-tree-select),
:deep(.post-tree-select) {
  width: 240px;
}
</style>
