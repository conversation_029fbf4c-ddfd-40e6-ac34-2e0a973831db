<template>
  <div class="online-course-detail-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <span class="label">课程创建时间</span>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DDTHH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 360px"
            @change="handleDateChange"
          />
        </div>
        <div class="filter-item">
          <span class="label">讲师部门筛选</span>
          <dept-tree-select
            v-model="queryParams.department"
            placeholder="选择部门"
            style="width: 240px"
          />
        </div>
        <!-- <div class="filter-item">
          <span class="label">讲师岗位筛选</span>
          <post-tree-select v-model="queryParams.position" placeholder="选择岗位" />
        </div> -->
        <div class="filter-item">
          <span class="label">模糊查询</span>
          <el-select v-model="queryParams.searchField" placeholder="查询字段" style="width: 120px">
            <el-option label="讲师姓名" value="lecturerName" />
            <el-option label="课程名称" value="courseName" />
            <el-option label="课程编号" value="courseCode" />
            <el-option label="讲师用户名" value="lecturerUsername" />
          </el-select>
          <el-input
            v-model="queryParams.keyword"
            placeholder="关键词"
            clearable
            style="width: 180px; margin-left: 8px"
            @keyup.enter="handleSearch"
          />
        </div>
      </div>
      <div class="filter-row">
        <div class="filter-item">
          <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
          <el-button @click="handleReset" style="margin-left: 10px">重置</el-button>
          <el-button
            type="primary"
            plain
            style="margin-left: 10px"
            @click="handleExport"
            :loading="exportLoading"
            :disabled="total === 0"
          >
            导出结果
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="courseList" border>
      <el-table-column label="课程名称" prop="courseName" min-width="150" show-overflow-tooltip />
      <el-table-column label="讲师用户名" prop="lecturerUsername" min-width="120" align="center" />
      <el-table-column label="讲师姓名" prop="lecturerName" min-width="120" align="center" />
      <el-table-column
        label="讲师所属部门"
        prop="lecturerDepartment"
        min-width="180"
        show-overflow-tooltip
      />
      <el-table-column label="讲师等级" prop="lecturerLevel" min-width="100" align="center" />
      <el-table-column label="上课时间" min-width="160" align="center">
        <template #default="scope">
          {{
            scope.row.startTime && scope.row.endTime
              ? `${scope.row.startTime} - ${scope.row.endTime}`
              : '--'
          }}
        </template>
      </el-table-column>
      <el-table-column label="上课地点" prop="courseLocation" min-width="120" align="center" />
      <el-table-column label="班级名称" prop="className" min-width="150" show-overflow-tooltip />
      <el-table-column label="报名人数" prop="signupCount" min-width="100" align="center" />
      <el-table-column label="全勤人数" prop="fullAttendanceCount" min-width="100" align="center" />
      <el-table-column
        label="课程学时(小时)"
        prop="courseDuration"
        min-width="120"
        align="center"
      />
      <el-table-column label="课程评价" prop="courseRating" min-width="120" align="center" />
      <el-table-column label="课程分类" prop="courseCategory" min-width="100" align="center" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入 API 函数
import {
  getOfflineCourseDetailList,
  exportOfflineCourseDetailList
} from '@/api/data-manage/teacher/teacher'
// 导入下载工具函数 (如果项目中有封装好的)
// import { downloadBlob } from '@/utils/download' // 假设路径

// 定义接口返回数据的类型 (可选，但推荐)
interface OfflineCourseDetail {
  id?: number // 假设接口会返回一个唯一ID，用于key
  courseName?: string
  courseCode?: string
  lecturerName?: string
  lecturerUsername?: string
  lecturerLevel?: string
  lecturerDepartment?: string
  createTime?: string
  courseCategory?: string
  courseLocation?: string
  joinCount?: number
  completeCount?: number
  courseDuration?: number
  courseRating?: number
  className?: string
  departmentName?: string // 这个字段似乎与 lecturerDepartment 重复，确认接口实际返回
  startTime?: string
  endTime?: string
  signupCount?: number
  fullAttendanceCount?: number
  absenceCount?: number
  leaveCount?: number
  homeworkCompletionRate?: string
  onlineExamPassRate?: string
  offlineExamPassRate?: string
}

// 查询参数 - startDate 和 endDate 初始化为空字符串或 null
const queryParams = reactive({
  department: null as number | number[] | null,
  position: null as string | number | null,
  searchField: 'lecturerName',
  keyword: '',
  startDate: '', // 直接使用
  endDate: '', // 直接使用
  pageNum: 1,
  pageSize: 10
})

// 日期范围
const dateRange = ref<[string, string] | undefined>(undefined)

// 日期选择器 change 事件
const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startDate = dateRange.value[0]
    queryParams.endDate = dateRange.value[1]
  } else {
    queryParams.startDate = ''
    queryParams.endDate = ''
  }
}

// 分页相关
const total = ref(0)
const loading = ref(false)
const exportLoading = ref(false)

// 表格数据
const courseList = ref<OfflineCourseDetail[]>([])

// 获取数据列表
const getList = async () => {
  loading.value = true
  try {
    // 准备参数时，startDate 和 endDate 已在 queryParams 中
    const params = {
      department: queryParams.department,
      position: queryParams.position,
      searchField: queryParams.searchField,
      keyword: queryParams.keyword,
      startDate: queryParams.startDate,
      endDate: queryParams.endDate,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize
    }

    const res = await getOfflineCourseDetailList(params as any)
    courseList.value = res.data || [] // 使用可选链和默认值增加健壮性
    total.value = res.data.total ? res.data.total : courseList.value.length
  } catch (error) {
    console.error('获取线下课程详情列表失败:', error)
    ElMessage.error('获取数据失败')
    courseList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索功能
const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置功能
const handleReset = () => {
  ElMessageBox.confirm('确定要重置所有筛选条件吗？', '重置确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      queryParams.department = null
      queryParams.position = null
      queryParams.searchField = 'lecturerName'
      queryParams.keyword = ''
      dateRange.value = undefined
      queryParams.startDate = ''
      queryParams.endDate = ''
      getList()
      ElMessage.success('筛选条件已重置')
    })
    .catch(() => {
      // 用户取消重置
    })
}

// 导出数据
const handleExport = async () => {
  if (total.value === 0) {
    ElMessage.warning('当前没有数据可以导出')
    return
  }

  try {
    await ElMessageBox.confirm('确认导出当前筛选条件下的所有数据吗？', '导出确认', {
      confirmButtonText: '确认导出',
      cancelButtonText: '取消',
      type: 'warning'
    })

    exportLoading.value = true

    // 准备导出参数，直接使用 queryParams 中的 startDate 和 endDate
    const exportParams: Record<string, any> = {
      department: queryParams.department,
      position: queryParams.position,
      searchField: queryParams.searchField,
      keyword: queryParams.keyword,
      startDate: queryParams.startDate,
      endDate: queryParams.endDate
    }

    // 移除空参数逻辑
    Object.keys(exportParams).forEach((key) => {
      const value = exportParams[key]
      // 根据接口要求调整此条件，如果 0 是有效值，则不应删除
      if (value === '' || value === null || value === undefined || value === 0) {
        delete exportParams[key]
      }
    })

    console.log('导出参数:', exportParams)

    const res = await exportOfflineCourseDetailList(exportParams)

    // --- 处理文件下载 (保持不变) ---
    if (res instanceof Blob) {
      const fileName = '线下课程明细导出.xlsx' // 或从响应头获取
      const blobUrl = window.URL.createObjectURL(res)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      ElMessage.success('导出任务已创建，请稍后查看下载文件')
    } else {
      console.error('导出失败，响应不是 Blob:', res)
      ElMessage.error('导出失败，响应格式不正确') // 更具体的错误提示
    }
    // --- 文件下载处理结束 ---
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出操作失败:', error) // 更明确的日志
      ElMessage.error('导出失败，请稍后重试或联系管理员')
    }
  } finally {
    exportLoading.value = false
  }
}

// 分页方法
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val
  getList()
}

// 暴露 getList 方法
defineExpose({
  getList
})

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.online-course-detail-tab {
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
    background-color: #f5f7fa;
    padding: 16px;
    font-size: 14px;
    border-radius: 4px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 5px;

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;

        .label {
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .user-avatar {
      background-color: #dce2f1;
      margin-right: 8px;
      color: #5a8def;
      font-size: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-name {
      font-weight: 500;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

:deep(.el-table__header) {
  th {
    font-weight: 600;
  }
}

:deep(.el-button.is-link) {
  padding: 0 5px;
  height: auto;
  font-size: 13px;
}
</style>
