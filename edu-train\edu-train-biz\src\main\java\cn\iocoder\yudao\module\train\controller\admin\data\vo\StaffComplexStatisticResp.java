package cn.iocoder.yudao.module.train.controller.admin.data.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.util.List;

@Data
public class StaffComplexStatisticResp {
    @ExcelIgnore
    private Long userId;

    @ExcelProperty("姓名")
    private String nickname;

    @ExcelProperty("岗位")
    private String postName;

    @ExcelProperty("部门")
    private String deptName;

    @ExcelIgnore
    private List<String> postNames; // 岗位名称列表

    @ExcelProperty("岗位列表")
    private String postNamesStr; // 用于Excel导出的岗位字符串

    // 线上课程
    @ExcelProperty("线上新增学时(分钟)")
    private Integer onlineNewStudyTime; // 新增学习时长

    @ExcelProperty("线上累计学时(分钟)")
    private Integer onlineTotalStudyTime; // 总学习时长

    @ExcelProperty("线上课程数")
    private Integer onlineCourseCount; // 学习课程数

    @ExcelProperty("线上完成数")
    private Integer onlineCourseCompleteCount; // 完成课程数

    // 线下课程
    @ExcelProperty("线下新增学时(分钟)")
    private Integer offlineNewStudyTime;

    @ExcelProperty("线下累计学时(分钟)")
    private Integer offlineTotalStudyTime;

    @ExcelProperty("线下课程数")
    private Integer offlineCourseCount;

    @ExcelProperty("线下完成数")
    private Integer offlineCourseCompleteCount;

    // 课程专题
    @ExcelProperty("专题新增学时(分钟)")
    private Integer topicNewStudyTime;

    @ExcelProperty("专题累计学时(分钟)")
    private Integer topicTotalStudyTime;

    @ExcelProperty("专题课程数")
    private Integer topicCourseCount;

    @ExcelProperty("专题完成数")
    private Integer topicCourseCompleteCount;

    // 培训项目
    @ExcelProperty("培训项目参加数")
    private Integer trainProjectJoinCount;

    // 线下活动
    @ExcelProperty("线下活动参加数")
    private Integer offlineActivityJoinCount=0;
}