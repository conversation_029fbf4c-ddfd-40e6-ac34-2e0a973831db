<template>
  <div class="course-overview-container">
    <!-- 讲师评价界面 -->
    <div v-if="route.query.type === 'evaluation'" class="instructor-evaluation">
      <!-- 主标题 -->
      <div class="evaluation-title">讲师评价</div>

      <!-- 汇总信息 -->
      <div class="summary-section">
        <div class="summary-item">
          <span class="label">已收问卷:</span>
          <span class="value green">1份</span>
        </div>
        <div class="separator"></div>
        <div class="summary-item">
          <span class="label">综合平均得分:</span>
          <span class="value green">3.50/5.00</span>
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </div>
      </div>

      <!-- 评价表格 -->
      <div class="evaluation-table">
        <el-table :data="evaluationData" style="width: 100%">
          <el-table-column label="名称" min-width="200">
            <template #default="{ row }">
              <span class="evaluation-name">{{ row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="问卷份数" width="150" align="center">
            <template #default="{ row }">
              <div class="questionnaire-count">
                <span>{{ row.receivedCount }}/{{ row.totalCount }}</span>
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="综合评价" width="150" align="center">
            <template #default="{ row }">
              <span class="evaluation-score">{{ row.averageScore }}/5.00</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center">
            <template #default="{ row }">
              <el-button size="small" @click="viewEvaluationDetail(row)"> 详情 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 课程概览界面 -->
    <div v-else>
      <!-- 课程标题 -->
      <div class="course-title">
        <span class="section-title">课程概览</span>
        <span class="course-name">{{ route.query.courseName || '课程名称' }}</span>
      </div>

      <!-- KPI指标卡片 -->
      <div class="kpi-section">
        <div class="kpi-grid">
          <div class="kpi-card">
            <div class="kpi-number">{{ courseStats.studentCount || 0 }}</div>
            <div class="kpi-label">课程学员数目</div>
          </div>
          <div class="kpi-card">
            <div class="kpi-number">{{ courseStats.completedCount || 0 }}</div>
            <div class="kpi-label">完成人数</div>
          </div>
          <div class="kpi-card">
            <div class="kpi-number">{{ courseStats.totalStudyHours || 0 }}</div>
            <div class="kpi-label">学习时长(小时)</div>
          </div>
          <div class="kpi-card">
            <div class="kpi-number">{{ courseStats.noteCount || 0 }}</div>
            <div class="kpi-label">笔记数</div>
          </div>
          <div class="kpi-card">
            <div class="kpi-number">{{ courseStats.questionCount || 0 }}</div>
            <div class="kpi-label">提问数</div>
          </div>
          <div class="kpi-card">
            <div class="kpi-number">{{ courseStats.topicCount || 0 }}</div>
            <div class="kpi-label">话题数</div>
          </div>
        </div>
      </div>

      <!-- 课程完成统计图表 -->
      <div class="completion-chart-section">
        <div class="chart-header">
          <div class="chart-title">
            <span>课程完成统计</span>
            <el-icon class="help-icon"><QuestionFilled /></el-icon>
          </div>
          <div class="chart-controls">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabledDate="disabledDate"
              @change="handleDateRangeChange"
              style="width: 300px"
            />
            <div class="quick-filters">
              <el-button
                size="small"
                :type="dateFilter === '7' ? 'primary' : 'default'"
                @click="setDateFilter('7')"
                >最近7天</el-button
              >
              <el-button
                size="small"
                :type="dateFilter === '30' ? 'primary' : 'default'"
                @click="setDateFilter('30')"
                >最近30天</el-button
              >
            </div>
          </div>
        </div>

        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot blue"></span>
            <span>每日完成人数</span>
          </div>
        </div>

        <div class="chart-container" ref="chartContainer">
          <div v-if="loading" class="chart-loading">
            <el-icon class="loading-icon" :size="24"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          <div v-else-if="!hasCompletionData" class="no-data">
            <span>暂无数据</span>
          </div>
          <div
            v-else
            ref="completionChartRef"
            class="chart-content"
            style="width: 100%; height: 400px"
          ></div>
        </div>
      </div>

      <!-- 学员详情/任务详情标签页 -->
      <div class="detail-tabs-section">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="学员详情" name="students">
            <div class="tab-content">
              <div class="tab-controls">
                <div class="control-group">
                  <span class="control-label">加入时间</span>
                  <el-select
                    v-model="studentFilter.joinTime"
                    placeholder="全部"
                    style="width: 120px"
                  >
                    <el-option label="全部" value="" />
                    <el-option label="最近7天" value="7days" />
                    <el-option label="最近30天" value="30days" />
                  </el-select>
                </div>
                <div class="control-group">
                  <span class="control-label">全部</span>
                  <el-select v-model="studentFilter.status" placeholder="全部" style="width: 120px">
                    <el-option label="全部" value="" />
                    <el-option label="学习中" value="learning" />
                    <el-option label="已完成" value="completed" />
                  </el-select>
                </div>
                <div class="search-box">
                  <el-input
                    v-model="searchKeyword"
                    placeholder="学员姓名"
                    prefix-icon="Search"
                    style="width: 250px"
                  >
                    <template #append>
                      <el-button @click="getStudentData">搜索</el-button>
                    </template>
                  </el-input>
                </div>
                <el-button type="primary" @click="handleExport">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>

              <div class="progress-legend">
                <div class="legend-item">
                  <span class="legend-dot green"></span>
                  <span>已学完</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot orange"></span>
                  <span>学习中</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot gray"></span>
                  <span>未学</span>
                </div>
              </div>

              <el-table :data="studentData" style="width: 100%" v-loading="loadingData">
                <el-table-column label="学员" min-width="200">
                  <template #default="{ row }">
                    <div class="student-info">
                      <div class="student-avatar">
                        <el-avatar :size="40">
                          {{ row.studentName?.charAt(0) || 'U' }}
                        </el-avatar>
                      </div>
                      <div class="student-details">
                        <div class="student-name">{{ row.studentName }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="学习进度" width="300">
                  <template #default="{ row }">
                    <div class="task-progress">
                      <div class="progress-bar">
                        <div
                          class="progress-segment completed"
                          :style="{ width: row.totalProgress + '%' }"
                        ></div>
                      </div>
                      <div class="progress-text">
                        {{
                          row.taskDetails?.filter((task) => task.status === 'completed')?.length ||
                          0
                        }}/ {{ row.taskDetails?.length || 0 }} 任务
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="完成率" width="120" align="center">
                  <template #default="{ row }">
                    <span class="completion-rate">{{ row.totalProgress || 0 }}%</span>
                  </template>
                </el-table-column>

                <el-table-column label="最后学习" width="180" align="center">
                  <template #default="{ row }">
                    <span>{{ row.lastStudyTime || '--' }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="120" align="center">
                  <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="viewStudentDetail(row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div class="pagination">
                <el-pagination :total="total" layout="total" />
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="任务详情" name="tasks">
            <div class="tab-content">
              <!-- 搜索和导出区域 -->
              <div class="task-controls">
                <div class="search-box">
                  <el-input
                    v-model="taskSearchKeyword"
                    placeholder="任务标题"
                    prefix-icon="Search"
                    style="width: 250px"
                    @input="handleTaskSearch"
                    clearable
                  />
                </div>
                <el-button type="primary" @click="handleTaskExport">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>

              <!-- 任务进度图例 -->
              <div class="task-progress-legend">
                <span class="legend-title">任务进度</span>
                <div class="legend-items">
                  <div class="legend-item">
                    <span class="legend-dot green"></span>
                    <span>已完成</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot orange"></span>
                    <span>进行中</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot gray"></span>
                    <span>未开始</span>
                  </div>
                </div>
              </div>

              <!-- 任务列表 -->
              <div class="task-list" v-loading="taskLoading">
                <div class="task-item" v-for="task in filteredTaskList" :key="task.taskId">
                  <div class="task-info">
                    <div class="task-header">
                      <div class="task-name">{{ task.taskTitle }}</div>
                      <div class="task-type">
                        <el-tag :type="getTaskTypeTag(task.taskType)" size="small">
                          {{ getTaskTypeName(task.taskType) }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="task-stats">
                      <div class="stat-item">
                        <span class="label">完成人数:</span>
                        <span class="value">{{ task.completedCount }}/{{ task.totalCount }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="label">完成率:</span>
                        <span class="value">{{ task.completionRate }}%</span>
                      </div>
                    </div>
                    <div class="task-progress">
                      <div class="progress-bar">
                        <div
                          class="progress-segment"
                          :class="getProgressClass(task.completionRate)"
                          :style="{ width: task.completionRate + '%' }"
                        ></div>
                      </div>
                      <div class="completion-rate">{{ task.completionRate }}%</div>
                    </div>
                  </div>
                </div>

                <!-- 暂无数据提示 -->
                <div v-if="!taskLoading && filteredTaskList.length === 0" class="empty-tip">
                  <span>暂无任务数据</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 浮动操作按钮 -->
      <div class="floating-actions">
        <el-button size="large" @click="handleFeedback"> 反馈 </el-button>
      </div>
    </div>

    <!-- 学员任务完成情况弹框 -->
    <el-dialog
      v-model="showTaskDetailDialog"
      :title="`${currentStudent?.studentName || '学员'}的任务完成情况`"
      width="800px"
      :before-close="handleCloseTaskDetail"
    >
      <div v-loading="taskCompletionLoading">
        <el-table :data="studentTaskCompletionList" style="width: 100%">
          <el-table-column prop="taskTitle" label="任务名称" />
          <el-table-column prop="taskType" label="任务类型">
            <template #default="scope">
              <span>{{ formatTaskType(scope.row.taskType) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="completionStatus" label="完成状态">
            <template #default="scope">
              <el-tag :type="scope.row.completionStatus === 1 ? 'success' : 'info'">
                {{ scope.row.completionStatus === 1 ? '已完成' : '未完成' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="completionTime" label="完成时间">
            <template #default="scope">
              <span>{{ scope.row.completionTime || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <el-button @click="handleCloseTaskDetail">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'

import {
  getCourseDetailStatistics,
  getStudentLearningDetail,
  getCourseCompletionStatistics,
  getTaskDetail,
  exportStudentLearningDetailExcel,
  exportTaskDetailExcel,
  getStudentTaskCompletion
} from '@/api/train/course/offlineCourses/statistics'
import * as echarts from 'echarts/core'
import { BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer
])

const route = useRoute()
const courseId = route.query.id ? route.query.id : undefined

// 标签页 - 提前声明以供watch使用
const activeTab = ref('students')

// 课程信息
const courseInfo = ref<any>(null)

// 课程统计数据
const courseStats = reactive({
  studentCount: 9,
  completedCount: 0,
  totalStudyHours: 0,
  noteCount: 0,
  questionCount: 0,
  topicCount: 0
})

// 图表相关
const dateRange = ref<[string, string] | null>(null)
const dateFilter = ref('7')
const chartInstance = ref<echarts.ECharts | null>(null)
const chartContainer = ref<HTMLElement | null>(null)
const loading = ref(false)
const hasCompletionData = ref(false)
const completionChartData = reactive({
  dates: [] as string[],
  completionCounts: [] as number[]
})

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// 处理日期范围变化
const handleDateRangeChange = (val: [string, string] | null) => {
  if (val) {
    dateFilter.value = '' // 清除快速筛选按钮的状态
    fetchCompletionStatistics(val[0], val[1])
  }
}

// 设置日期筛选
const setDateFilter = (days: string) => {
  dateFilter.value = days

  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - parseInt(days))

  // 格式化日期为yyyy-MM-dd
  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  const startDateStr = formatDate(start)
  const endDateStr = formatDate(end)

  dateRange.value = [startDateStr, endDateStr]
  fetchCompletionStatistics(startDateStr, endDateStr)
}

// 获取课程完成统计数据
const fetchCompletionStatistics = async (startDate: string, endDate: string) => {
  if (!courseId) {
    ElMessage.error('课程ID不能为空')
    return
  }

  loading.value = true
  hasCompletionData.value = false

  try {
    const res = await getCourseCompletionStatistics({
      courseId: String(courseId),
      startDate,
      endDate
    })

    console.log('API返回数据:', res)

    if (res.code === 200 && res.data) {
      // 解析API返回的数据
      completionChartData.dates = res.data.dateList || []
      completionChartData.completionCounts = res.data.completionCountList || []

      // 检查是否有日期数据 - 只要有日期数据就显示图表，即使所有完成人数都是0
      hasCompletionData.value = completionChartData.dates && completionChartData.dates.length > 0

      console.log('处理后的图表数据:', {
        dates: completionChartData.dates,
        counts: completionChartData.completionCounts,
        hasData: hasCompletionData.value
      })

      // 确保DOM渲染完成后再初始化图表
      // 使用setTimeout增加延迟，确保DOM已完全渲染
      nextTick(() => {
        setTimeout(() => {
          initCompletionChart()
        }, 100)
      })
    } else {
      ElMessage.error(res.msg || '获取课程完成统计数据失败')
    }
  } catch (error) {
    console.error('获取课程完成统计数据失败:', error)
    ElMessage.error('获取课程完成统计数据失败')
  } finally {
    loading.value = false
  }
}

// 创建图表容器ref
const completionChartRef = ref<HTMLElement | null>(null)

// 初始化图表
const initCompletionChart = () => {
  console.log('初始化图表，数据:', completionChartData)

  // 使用ref获取DOM元素
  const chartDom = completionChartRef.value
  if (!chartDom) {
    console.error('图表容器不存在! completionChartRef未绑定到DOM元素')
    return
  }

  // 确保容器尺寸正确
  if (chartDom.clientHeight === 0) {
    console.warn('图表容器高度为0，设置默认高度')
    chartDom.style.height = '400px'
  }

  // 销毁之前的图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
    console.log('销毁旧图表实例')
  }

  // 创建新的图表实例
  try {
    chartInstance.value = echarts.init(chartDom)
    console.log('创建新图表实例成功')
  } catch (err) {
    console.error('创建图表实例失败:', err)
    return
  }

  // 图表配置项
  const option = {
    title: {
      text: '课程完成人数统计',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      },
      top: 0,
      padding: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}人`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '60px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: completionChartData.dates,
      axisLabel: {
        interval: 0,
        rotate: completionChartData.dates.length > 7 ? 45 : 0, // 日期过多时斜着显示
        formatter: function (value: string) {
          // 只显示月和日
          return value.substring(5) // 截取MM-DD部分
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '人数',
      minInterval: 1, // 最小间隔为1，确保坐标轴刻度为整数
      min: 0, // 确保Y轴从0开始
      max: function (value) {
        // 如果所有值都是0，则设置最大值为5，以便显示坐标轴
        return value.max > 0 ? value.max : 5
      }
    },
    series: [
      {
        name: '每日完成人数',
        type: 'bar',
        data: completionChartData.completionCounts,
        barWidth: '40%', // 柱子宽度
        itemStyle: {
          color: '#409EFF'
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ]
  }

  // 使用配置项设置图表
  try {
    chartInstance.value.setOption(option)
    console.log('图表配置已应用')
  } catch (err) {
    console.error('设置图表配置失败:', err)
  }

  // 窗口大小变化时自动调整图表大小
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance.value) {
    console.log('窗口大小变化，调整图表大小')
    chartInstance.value.resize()
  }
}

// 确保在数据变化时更新图表
watch(
  [completionChartData.dates, completionChartData.completionCounts],
  () => {
    if (hasCompletionData.value && chartInstance.value) {
      console.log('数据更新，重新渲染图表')
      nextTick(() => {
        chartInstance.value?.resize()
      })
    }
  },
  { deep: true }
)

// 监听标签页切换，确保图表在显示时重新渲染
watch(activeTab, (newTab) => {
  console.log('标签页切换为:', newTab)
  // 延迟执行，确保DOM已更新
  nextTick(() => {
    if (newTab !== 'students' && chartInstance.value && hasCompletionData.value) {
      setTimeout(() => {
        console.log('标签切换后重新渲染图表')
        chartInstance.value?.resize()
      }, 300)
    }
  })
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
})

// 此处已经在文件前部分声明了activeTab

// 学员筛选
const studentFilter = reactive({
  joinTime: '',
  status: ''
})

// 搜索和分页
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loadingData = ref(false)
const studentData = ref<any[]>([])

// 任务列表
const taskList = ref<any[]>([])
const taskLoading = ref(false)

// 任务详情搜索关键词
const taskSearchKeyword = ref('')

// 过滤任务列表
const filteredTaskList = ref<any[]>([])

// 获取任务详情数据
const fetchTaskDetails = async () => {
  if (!courseId) {
    ElMessage.error('课程ID不能为空')
    return
  }

  taskLoading.value = true

  try {
    const params = {
      courseId: String(courseId),
      taskTitle: taskSearchKeyword.value || undefined
    }

    const res = await getTaskDetail(params)
    if (res.code === 200 && res.data) {
      taskList.value = res.data
      filteredTaskList.value = res.data
    } else {
      taskList.value = []
      filteredTaskList.value = []
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
    taskList.value = []
    filteredTaskList.value = []
  } finally {
    taskLoading.value = false
  }
}

// 任务搜索处理
const handleTaskSearch = () => {
  if (!taskSearchKeyword.value) {
    filteredTaskList.value = taskList.value
  } else {
    filteredTaskList.value = taskList.value.filter((task) =>
      task.taskTitle.toLowerCase().includes(taskSearchKeyword.value.toLowerCase())
    )
  }
}

// 任务导出处理
const handleTaskExport = async () => {
  // 显示加载提示
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在导出数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    if (!courseId) {
      ElMessage.error('课程ID不能为空')
      loadingInstance.close()
      return
    }

    // 创建参数对象
    const params = {
      courseId: String(courseId),
      taskTitle: taskSearchKeyword.value || undefined
    }

    // 调用API导出函数
    const blob = await exportTaskDetailExcel(params)

    // 确保加载提示关闭
    loadingInstance.close()

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `任务详情_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    // 确保加载提示关闭
    loadingInstance.close()
    console.error('导出任务详情失败:', error)
    ElMessage.error('导出失败')
  }
}

// 获取任务类型标签样式
const getTaskTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    document: 'success',
    exercise: 'warning',
    homework: 'danger',
    discussion: 'info',
    download: 'primary',
    audio: 'success',
    evaluation: 'warning',
    video: 'primary',
    ppt: 'success',
    survey: 'info',
    exam: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取任务类型显示名称
const getTaskTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    document: '文档',
    exercise: '练习',
    homework: '作业',
    discussion: '讨论',
    download: '下载',
    audio: '音频',
    evaluation: '评价',
    video: '视频',
    ppt: 'PPT',
    survey: '问卷',
    exam: '考试'
  }
  return typeMap[type] || type
}

// 获取进度条样式类
const getProgressClass = (rate: number) => {
  if (rate >= 80) return 'completed'
  if (rate >= 20) return 'learning'
  return 'not-started'
}

// 讲师评价数据
const evaluationData = ref([
  {
    id: 1,
    name: '评价问卷',
    receivedCount: 1,
    totalCount: 9,
    averageScore: '3.50'
  }
])

// 查看评价详情
const viewEvaluationDetail = (row) => {
  ElMessage.info(`查看评价详情: ${row.name}`)
}

// 原来的setDateFilter函数已移至上方实现

// 获取学员数据
const getStudentData = async () => {
  try {
    if (!courseId) {
      ElMessage.error('课程ID不能为空')
      return
    }

    loadingData.value = true
    const params = {
      courseId: courseId,
      studentName: searchKeyword.value || undefined
    }

    const res = await getStudentLearningDetail(params)
    if (res.code === 200 && res.data) {
      studentData.value = res.data
      total.value = res.data.length || 0
    } else {
      studentData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取学员数据失败:', error)
    ElMessage.error('获取学员数据失败')
  } finally {
    loadingData.value = false
  }
}

// 获取课程统计信息
const getCourseStats = async () => {
  try {
    if (!courseId) return

    // 调用线上课程概览接口获取统计信息
    const res = await getCourseDetailStatistics(String(courseId))

    if (res.code === 200 && res.data) {
      courseStats.studentCount = res.data.studentCount || 0
      courseStats.completedCount = res.data.completedCount || 0
      courseStats.totalStudyHours = res.data.learnDurationHours || 0
      courseStats.noteCount = res.data.noteCount || 0
      courseStats.questionCount = res.data.questionCount || 0
      // 使用提问数或读题数作为话题数，因为API没有直接返回话题数
      courseStats.topicCount = res.data.readQuestionCount || 0
    }
  } catch (error) {
    console.error('获取课程统计信息失败:', error)
  }
}

// 学员详情弹框相关
const showStudentDetailDialog = ref(false)
const currentStudent = ref<any>(null)

// 任务详情弹框相关
const showTaskDetailDialog = ref(false)

// 学员任务完成情况数据
const studentTaskCompletionList = ref([])
const taskCompletionLoading = ref(false)

// 查看学员详情
const viewStudentDetail = async (row) => {
  try {
    // 直接使用API返回的详细数据
    const studentDetail = {
      studentId: row.studentId,
      studentName: row.studentName,
      updateTime: row.lastStudyTime || '--',
      // 学习数据
      learningTime: row.learningTime || 0,
      totalProgress: row.totalProgress || 0,
      totalScore: row.totalScore || 0
    }

    currentStudent.value = studentDetail
    showTaskDetailDialog.value = true

    // 获取学员任务完成情况
    await fetchStudentTaskCompletion()
  } catch (error) {
    console.error('获取学员详情失败:', error)
    ElMessage.error('获取学员详情失败')
  }
}

// 关闭学员详情弹框
const handleCloseStudentDetail = () => {
  showStudentDetailDialog.value = false
  currentStudent.value = null
}

// 关闭任务详情弹框
const handleCloseTaskDetail = () => {
  showTaskDetailDialog.value = false
  currentStudent.value = null
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  const statusMap = {
    completed: '已完成',
    learning: '进行中',
    'not-started': '未开始'
  }
  return statusMap[status] || '未知'
}

// 导出功能
const handleExport = async () => {
  // 显示加载提示
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在导出数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    if (!courseId) {
      ElMessage.error('课程ID不能为空')
      loadingInstance.close()
      return
    }

    // 创建参数对象
    const params = {
      courseId: String(courseId),
      studentName: searchKeyword.value || undefined
    }

    // 调用API导出函数
    const blob = await exportStudentLearningDetailExcel(params)

    // 确保加载提示关闭
    loadingInstance.close()

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `学员学习详情_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    // 确保加载提示关闭
    loadingInstance.close()
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 反馈功能
const handleFeedback = () => {
  ElMessage.info('打开反馈页面')
}

// 监听搜索关键词变化
watch([searchKeyword], () => {
  getStudentData()
})

// 监听任务搜索关键词变化
watch(taskSearchKeyword, (newVal) => {
  filteredTaskList.value = taskList.value.filter((task) => task.name.includes(newVal))
})

onMounted(() => {
  if (!courseId) {
    ElMessage.error('课程ID不能为空')
    return
  }
  getStudentData()
  getCourseStats()

  // 初始化图表，默认显示最近7天的数据
  setDateFilter('7')

  // 初始化任务详情数据
  fetchTaskDetails()
})

/**
 * 获取学员任务完成情况数据
 */
const fetchStudentTaskCompletion = async () => {
  if (!courseId) return

  try {
    taskCompletionLoading.value = true
    const res = await getStudentTaskCompletion(courseId)

    // 如果有当前学员ID，过滤只显示当前学员的数据
    if (res.code === 200 && res.data && currentStudent.value?.studentId) {
      studentTaskCompletionList.value = res.data.filter(
        (item) => item.studentId === currentStudent.value.studentId
      )
    } else {
      studentTaskCompletionList.value = res.data || []
    }
  } catch (error) {
    console.error('获取学员任务完成情况失败:', error)
    ElMessage.error('获取学员任务完成情况失败')
  } finally {
    taskCompletionLoading.value = false
  }
}

/**
 * 格式化任务类型
 * @param type 任务类型标识
 */
const formatTaskType = (type) => {
  const typeMap = {
    video: '视频',
    document: '文档',
    exam: '考试',
    survey: '问卷',
    live: '直播',
    homework: '作业'
  }
  return typeMap[type] || type
}
</script>

<style lang="scss" scoped>
.course-overview-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;

  .course-title {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 20px;
    padding: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 16px;

      color: #909399;
      margin-right: 8px;
    }

    .course-name {
      font-size: 16px;
    }
  }

  .kpi-section {
    margin-bottom: 20px;

    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .kpi-card {
        background: #fff;
        padding: 24px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .kpi-number {
          font-size: 32px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 8px;
        }

        .kpi-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .completion-chart-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .chart-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #909399;

        .help-icon {
          margin-left: 8px;
          color: #909399;
          cursor: pointer;
        }
      }

      .chart-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .quick-filters {
          display: flex;
          gap: 8px;
        }
      }
    }

    .chart-legend {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #606266;

        .legend-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.green {
            background-color: #67c23a;
          }

          &.orange {
            background-color: #e6a23c;
          }

          &.blue {
            background-color: #409eff;
          }
        }
      }
    }

    .chart-container {
      height: 400px;
      position: relative;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;

      .chart-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 10;

        .loading-icon {
          animation: rotating 2s linear infinite;
          margin-bottom: 8px;
        }
      }

      .no-data {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #909399;
        font-size: 14px;
      }

      .chart-content {
        width: 100%;
        height: 100%;
      }
    }

    @keyframes rotating {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }

  .detail-tabs-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .tab-content {
      padding: 20px;

      .tab-controls {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 20px;

        .control-group {
          display: flex;
          align-items: center;
          gap: 8px;

          .control-label {
            font-size: 14px;
            color: #606266;
          }
        }

        .search-box {
          margin-left: auto;
        }
      }

      .progress-legend {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #606266;

          .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.green {
              background-color: #67c23a;
            }

            &.orange {
              background-color: #e6a23c;
            }

            &.gray {
              background-color: #909399;
            }
          }
        }
      }

      .student-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .student-details {
          .student-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .student-dept {
            font-size: 12px;
            color: #909399;
          }
        }
      }

      .task-progress {
        .progress-bar {
          display: flex;
          height: 8px;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 8px;

          .progress-segment {
            height: 100%;

            &.completed {
              background-color: #67c23a;
            }

            &.learning {
              background-color: #e6a23c;
            }

            &.not-started {
              background-color: #909399;
            }
          }
        }

        .progress-text {
          font-size: 12px;
          color: #606266;
        }
      }

      .completion-rate {
        font-weight: 500;
        color: #409eff;
      }

      .task-list {
        .task-item {
          padding: 16px;
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          margin-bottom: 12px;
          background-color: #fff;

          .task-info {
            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .task-name {
                font-weight: 500;
                color: #303133;
                flex: 1;
                margin-right: 16px;
                font-size: 14px;
              }

              .task-type {
                flex-shrink: 0;
              }
            }

            .task-stats {
              display: flex;
              gap: 20px;
              margin-bottom: 12px;

              .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;

                .label {
                  color: #909399;
                }

                .value {
                  color: #303133;
                  font-weight: 500;
                }
              }
            }

            .task-progress {
              display: flex;
              align-items: center;
              gap: 8px;

              .progress-bar {
                flex: 1;
                height: 8px;
                border-radius: 4px;
                overflow: hidden;
                background-color: #f0f0f0;

                .progress-segment {
                  height: 100%;
                  border-radius: 4px;
                  transition: width 0.3s ease;

                  &.completed {
                    background-color: #67c23a;
                  }

                  &.learning {
                    background-color: #e6a23c;
                  }

                  &.not-started {
                    background-color: #909399;
                  }
                }
              }

              .completion-rate {
                font-size: 12px;
                color: #606266;
                min-width: 40px;
                text-align: right;
                font-weight: 500;
              }
            }
          }
        }

        .empty-tip {
          text-align: center;
          padding: 40px 0;
          color: #909399;
          font-size: 14px;
        }
      }

      // 任务详情页面特有样式
      .task-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .search-box {
          flex: 1;
        }
      }

      .task-progress-legend {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 20px;
        padding: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .legend-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
        }

        .legend-items {
          display: flex;
          gap: 20px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #606266;

            .legend-dot {
              width: 12px;
              height: 12px;
              border-radius: 50%;

              &.green {
                background-color: #67c23a;
              }

              &.orange {
                background-color: #e6a23c;
              }

              &.gray {
                background-color: #909399;
              }
            }
          }
        }
      }

      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }

  .floating-actions {
    position: fixed;
    right: 20px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 1000;

    .el-button {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  // 讲师评价界面样式
  .instructor-evaluation {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;

    .evaluation-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
    }

    .summary-section {
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 20px;

      .summary-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          font-size: 14px;
          color: #606266;
        }

        .value {
          font-size: 14px;
          font-weight: 500;

          &.green {
            color: #67c23a;
          }
        }

        .help-icon {
          color: #909399;
          cursor: pointer;
          margin-left: 4px;
        }
      }

      .separator {
        width: 1px;
        height: 20px;
        background-color: #e4e7ed;
        margin: 0 20px;
      }
    }

    .evaluation-table {
      .evaluation-name {
        font-weight: 500;
        color: #303133;
      }

      .questionnaire-count {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        .help-icon {
          color: #909399;
          cursor: pointer;
        }
      }

      .evaluation-score {
        font-weight: 500;
        color: #67c23a;
      }
    }
  }
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  padding: 0 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__item) {
  padding: 16px 20px;
  font-size: 14px;
  color: #606266;

  &.is-active {
    color: #409eff;
    font-weight: 500;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .update-time {
    font-size: 12px;
    color: #909399;
  }
}

// 任务详情弹框样式
.task-detail-content {
  .task-name {
    font-weight: 500;
    color: #303133;
  }
}
</style>
