<template>
  <div class="detail-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <span class="label">数据时间筛选</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            value-format="YYYY/MM/DD"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </div>
        <div class="filter-item">
          <span class="label">部门筛选</span>
          <dept-tree-select v-model="queryParams.department" style="width: 240px" />
        </div>
        <div class="filter-item">
          <span class="label">岗位筛选</span>
          <post-tree-select v-model="queryParams.position" placeholder="选择岗位" />
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-item">
          <span class="label">精准查询</span>
          <el-select v-model="queryParams.searchType" placeholder="姓名" style="width: 120px">
            <el-option label="姓名" value="nickName" />
            <el-option label="用户名" value="userName" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-input
            v-model="queryParams.keyword"
            placeholder="关键词"
            style="width: 200px; margin-right: 10px"
          />
        </div>
        <div class="filter-item">
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </div>
        <div class="filter-item">
          <el-button @click="handleReset">重置</el-button>
        </div>
        <div class="filter-item">
          <el-button @click="handleExport">导出结果</el-button>
        </div>
        <!-- <div class="filter-item">
          <el-button @click="showColumnDialog">自定义列</el-button>
        </div> -->
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="userList"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column label="姓名" width="150" fixed="left">
        <template #default="{ row }">
          <div class="user-avatar-name">
            <el-avatar :size="32" :src="row.avatar">{{ row.nickname?.charAt(0) || 'U' }}</el-avatar>
            <span class="user-name">{{ row.nickname || '--' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="岗位" prop="postNames" width="180">
        <template #default="{ row }">
          <div v-if="row.postNames && row.postNames.length > 0">
            <el-tag
              v-for="(postName, index) in row.postNames"
              :key="index"
              size="small"
              style="margin-right: 4px; margin-bottom: 2px;"
            >
              {{ postName }}
            </el-tag>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column label="所属部门" prop="deptName" min-width="180">
        <template #default="{ row }">
          <span>{{ row.deptName || '--' }}</span>
        </template>
      </el-table-column>

      <!-- 线上课程栏目 -->
      <el-table-column label="线上课程" align="center">
        <el-table-column label="新增学时(分钟)" prop="onlineNewStudyTime" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.onlineNewStudyTime || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="累计学时(分钟)" prop="onlineTotalStudyTime" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.onlineTotalStudyTime || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="课程数" prop="onlineCourseCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.onlineCourseCount || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="完成数" prop="onlineCourseCompleteCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.onlineCourseCompleteCount || 0 }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <!-- 线下课程栏目 -->
      <el-table-column label="线下课程" align="center">
        <el-table-column label="新增学时(分钟)" prop="offlineNewStudyTime" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.offlineNewStudyTime || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="累计学时(分钟)" prop="offlineTotalStudyTime" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.offlineTotalStudyTime || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="课程数" prop="offlineCourseCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.offlineCourseCount || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="完成数" prop="offlineCourseCompleteCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.offlineCourseCompleteCount || 0 }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <!-- 课程专题栏目 -->
      <el-table-column label="课程专题" align="center">
        <el-table-column label="新增学时(分钟)" prop="topicNewStudyTime" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.topicNewStudyTime || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="累计学时(分钟)" prop="topicTotalStudyTime" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.topicTotalStudyTime || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="课程数" prop="topicCourseCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.topicCourseCount || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="完成数" prop="topicCourseCompleteCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.topicCourseCompleteCount || 0 }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <!-- 培训项目栏目 -->
      <el-table-column label="培训项目" align="center">
        <el-table-column label="参加数" prop="trainProjectJoinCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.trainProjectJoinCount || 0 }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <!-- 线下活动栏目 -->
      <el-table-column label="线下活动" align="center">
        <el-table-column label="参加数" prop="offlineActivityJoinCount" width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.offlineActivityJoinCount || 0 }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="操作" width="260" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="viewLearningRecord(row)">学习档案</el-button>
          <el-button type="primary" link @click="viewPersonalInfo(row)">个人信息</el-button>
          <el-button type="primary" link @click="downloadRecord(row)">下载记录</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        background
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 自定义列对话框 -->
    <el-dialog v-model="columnDialogVisible" title="自定义列" width="500px">
      <el-checkbox-group v-model="selectedColumns">
        <div class="column-item" v-for="column in availableColumns" :key="column.prop">
          <el-checkbox :label="column.prop">{{ column.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="columnDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyColumns">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <user-detail-dialog v-model="userDetailVisible" :username="currentUsername" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { UserTrainingApi } from '@/api/data-manage/user/user'

// 定义用户数据接口
interface UserData {
  userId: number
  nickname: string
  postName: string | null
  deptName: string
  postNames: string[] // 岗位名称列表
  onlineNewStudyTime: number
  onlineTotalStudyTime: number
  onlineCourseCount: number
  onlineCourseCompleteCount: number
  offlineNewStudyTime: number
  offlineTotalStudyTime: number
  offlineCourseCount: number
  offlineCourseCompleteCount: number
  topicNewStudyTime: number
  topicTotalStudyTime: number
  topicCourseCount: number
  topicCourseCompleteCount: number
  trainProjectJoinCount: number
  offlineActivityJoinCount: number
  avatar?: string
}

// 日期范围
const defaultDateRange = [
  new Date(2025, 0, 1), // 2025/01/01
  new Date(2025, 11, 31) // 2025/12/31
] as [Date, Date]
const dateRange = ref<[Date, Date] | undefined>(undefined)

// 查询参数
const queryParams = reactive({
  department: null as number | null,
  position: '',
  level: '',
  userGroup: '',
  entryTimeOp: 'gte',
  entryTime: '',
  banStatus: 'normal',
  searchType: 'nickName',
  keyword: '',
  beginTime: '',
  endTime: ''
})

// 表格数据
const loading = ref(false)
const userList = ref<UserData[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 列配置
const columnDialogVisible = ref(false)
const availableColumns = [
  { label: '姓名', prop: 'nickname' },
  { label: '岗位', prop: 'postName' },
  { label: '所属部门', prop: 'deptName' },
  { label: '线上新增学时', prop: 'onlineNewStudyTime' },
  { label: '线上累计学时', prop: 'onlineTotalStudyTime' },
  { label: '线上课程数', prop: 'onlineCourseCount' },
  { label: '线上完成数', prop: 'onlineCourseCompleteCount' },
  { label: '线下新增学时', prop: 'offlineNewStudyTime' },
  { label: '线下累计学时', prop: 'offlineTotalStudyTime' }
]
const selectedColumns = ref([
  'nickname',
  'postName',
  'deptName',
  'onlineNewStudyTime',
  'onlineTotalStudyTime',
  'onlineCourseCount',
  'onlineCourseCompleteCount',
  'offlineNewStudyTime',
  'offlineTotalStudyTime'
])

// 用户详情对话框相关
const userDetailVisible = ref(false)
const currentUsername = ref('')

// 搜索方法
const handleSearch = () => {
  loadUserData()
}

// 重置方法
const handleReset = () => {
  dateRange.value = defaultDateRange
  queryParams.department = null
  queryParams.position = ''
  queryParams.searchType = 'nickName'
  queryParams.keyword = ''
  loadUserData()
}

// 获取用户数据
const loadUserData = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params: any = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      deptId: queryParams.department || undefined,
      postId: queryParams.position || undefined
    }

    // 根据搜索类型传递对应参数
    if (queryParams.searchType === 'userName') {
      params.userName = queryParams.keyword
    } else if (queryParams.searchType === 'nickName') {
      params.nickName = queryParams.keyword
    }

    // 如果选择了日期范围，则添加到参数中
    if (dateRange.value && dateRange.value.length === 2) {
      params.beginTime = dateRange.value[0].getTime()
      params.endTime = dateRange.value[1].getTime()
    }

    const response = await UserTrainingApi.getStaffComplexStatistics(params)

    if (response && response.code === 200) {
      // 直接使用API响应数据，因为字段名已经匹配
      userList.value = response.data?.list || []
      total.value = response.data?.total || userList.value.length
    } else {
      ElMessage.error({ message: response?.msg || '获取用户数据失败' })
      userList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取用户数据失败:', error)
    userList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页方法
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadUserData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadUserData()
}

// 导出结果
const handleExport = async () => {
  try {
    // 构建导出参数
    const params: any = {
      deptId: queryParams.department || undefined,
      postId: queryParams.position || undefined
    }

    // 根据搜索类型传递对应参数
    if (queryParams.searchType === 'userName') {
      params.userName = queryParams.keyword
    } else if (queryParams.searchType === 'nickName') {
      params.nickName = queryParams.keyword
    }

    // 如果选择了日期范围，则添加到参数中
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    await UserTrainingApi.exportStaffComplexStatistics(params)
    ElMessage.success({ message: '员工数据明细导出成功' })
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error({ message: '导出员工数据明细失败' })
  }
}

// 自定义列相关
const showColumnDialog = () => {
  columnDialogVisible.value = true
}

const applyColumns = () => {
  // 应用所选列
  columnDialogVisible.value = false
  ElMessage.success({ message: '列设置已保存' })
}

const router = useRouter()

// 操作方法
const viewLearningRecord = (row: UserData) => {
  ElMessage.info({ message: `查看 ${row.nickname} 的学习档案` })
  router.push({
    path: '/data/learn_draw'
    // query: {
    //   type: 'learningRecord',
    //   username: row.nickname
    // }
  })
}

const viewPersonalInfo = (row: UserData) => {
  ElMessage.info({ message: `查看 ${row.nickname} 的个人信息` })
}

const downloadRecord = (row: UserData) => {
  ElMessage.info({ message: `下载 ${row.nickname} 的记录` })
}

onMounted(() => {
  loadUserData()
})
</script>

<style lang="scss" scoped>
.detail-tab {
  .filter-section {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;
    background-color: #f5f7fa;
    padding: 16px;
    font-size: 14px;
    border-radius: 4px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;

        .label {
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
  }

  .user-avatar-name {
    display: flex;
    align-items: center;

    .user-name {
      margin-left: 12px;
      font-weight: 500;
    }
  }

  .dept-code {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .column-item {
    margin-bottom: 10px;
  }
}

:deep(.el-table__header) {
  th {
    font-weight: 600;
  }
}

:deep(.el-button.is-link) {
  padding: 0 5px;
  height: auto;
  font-size: 13px;
}
</style>
