<template>
  <div class="overview-container">
    <!-- 搜索区域 -->
    <div class="detail-search">
      <div class="white-block">
        <div class="search-form">
          <el-form :inline="true">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DDTHH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                style="width: 360px"
                @change="handleDateChange"
              />
            </el-form-item>
            <el-form-item label="所属部门">
              <el-tree-select
                v-model="searchForm.deptId"
                :data="deptList"
                :props="{
                  value: 'id',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择部门"
                clearable
                check-strictly
                default-expand-all
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="课程分类">
              <el-tree-select
                v-model="searchForm.categoryId"
                :data="categoryList"
                :props="{
                  value: 'id',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择课程分类"
                clearable
                check-strictly
                default-expand-all
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="标题">
              <el-input
                v-model="searchForm.title"
                placeholder="请输入课程标题"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择状态"
                clearable
                style="width: 200px"
              >
                <el-option label="已发布" value="1" />
                <el-option label="未发布" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="讲师">
              <el-input
                v-model="searchForm.lecturer"
                placeholder="请输入讲师姓名"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="success" @click="handleExport">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="detail-search">
      <el-table
        :data="filteredTableData"
        style="width: 100%"
        :header-cell-style="{ background: '#F5F5F5' }"
      >
        <el-table-column fixed prop="title" label="课程名称" min-width="280">
          <template #default="{ row }">
            <div class="course-info">
              <el-image
                :src="row.coverImageUrl || ''"
                class="course-image"
                fit="cover"
                :preview-src-list="[row.coverImageUrl || '']"
                :initial-index="0"
                :preview-teleported="true"
                :hide-on-click-modal="false"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="course-content">
                <span class="course-title">{{ row.title }}</span>
                <div class="course-stats">
                  <span>
                    <el-icon><User /></el-icon>
                    {{ row.studentCount || 0 }}人学习
                  </span>
                  <span>
                    <el-icon><Timer /></el-icon>
                    {{ row.courseDuration || '0分钟' }}
                  </span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="teacherName" label="讲师" min-width="120" />
        <el-table-column prop="createTime" label="创建时间" min-width="120">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="deptName" label="所属部门" min-width="200" show-overflow-tooltip />
        <el-table-column prop="categoryName" label="分类" min-width="120" />
        <el-table-column prop="studentCount" label="学员数" min-width="100" />
        <el-table-column prop="completedCount" label="完课人数" min-width="100" />
        <el-table-column prop="courseDuration" label="课程总时长" min-width="120" />
        <el-table-column prop="learnDuration" label="学习总时长" min-width="120" />
        <el-table-column prop="avgLearnDuration" label="人均学习时长" min-width="120" />
        <el-table-column prop="score" label="综合评价" min-width="100" />
        <el-table-column fixed="right" label="操作" min-width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleDetail(row)">详细数据</el-button>
            <el-button type="primary" link @click="handleEvaluation(row)">教学评价</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="page.pageNo"
        v-model:page-size="page.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CourseDataApi } from '@/api/data/train/course/index'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/formatTime'
import { useRouter } from 'vue-router'
import * as DeptApi from '@/api/system/dept'
import { CourseCategoryApi } from '@/api/train/course/courseCategory'
import { handleTree } from '@/utils/tree'

// 定义表格数据的类型
interface TableItem {
  id: number
  title: string
  createTime: string
  deptName: string
  categoryName: string
  studentCount: number
  completedCount: number
  courseDuration: string
  learnDuration: string
  avgLearnDuration: string
  score: number
  teacherId: number
  teacherName: string
  coverImageUrl: string
  status: string | number
}

const router = useRouter()

// 部门列表
const deptList = ref<any[]>([])
// 课程分类列表
const categoryList = ref<any[]>([])

// 获取部门列表
const getDeptList = async () => {
  try {
    const data = await DeptApi.getSimpleDeptList()
    deptList.value = handleTree(data)
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取课程分类列表
const getCategoryList = async () => {
  try {
    const data = await CourseCategoryApi.getCourseCategoryTree()
    categoryList.value = data
  } catch (error) {
    console.error('获取课程分类列表失败:', error)
  }
}

const searchForm = ref({
  startTime: '',
  endTime: '',
  deptId: undefined as unknown as number,
  categoryId: undefined as unknown as number,
  title: undefined as unknown as string,
  status: undefined as unknown as string,
  lecturer: undefined as unknown as string
})

const page = ref({
  pageNo: 1,
  pageSize: 10
})
const total = ref(0)
const tableData = ref<TableItem[]>([])
const filteredTableData = ref<TableItem[]>([])

// 日期范围
const dateRange = ref<[string, string] | undefined>(undefined)

// 处理日期范围变化
const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    searchForm.value.startTime = dateRange.value[0]
    searchForm.value.endTime = dateRange.value[1]
  } else {
    searchForm.value.startTime = ''
    searchForm.value.endTime = ''
  }
}

// 查询
const handleSearch = async () => {
  page.value.pageNo = 1 // 重置到第一页
  await getTableData()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    startTime: '',
    endTime: '',
    deptId: undefined as unknown as number,
    categoryId: undefined as unknown as number,
    title: undefined as unknown as string,
    status: undefined as unknown as string,
    lecturer: undefined as unknown as string
  }
  dateRange.value = undefined // 重置日期范围
  page.value.pageNo = 1
  getTableData()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  page.value.pageSize = size
  page.value.pageNo = 1 // 切换每页条数时重置为第一页
  getTableData()
}

// 页码变化
const handlePageChange = (num: number) => {
  page.value.pageNo = num
  getTableData()
}

const getTableData = async () => {
  try {
    // 从searchForm中提取API需要的参数
    const params = {
      pageNo: page.value.pageNo,
      pageSize: page.value.pageSize,
      title: searchForm.value.title,
      deptId: searchForm.value.deptId,
      categoryId: searchForm.value.categoryId,
      status: searchForm.value.status,
      lecturer: searchForm.value.lecturer,
      startTime: searchForm.value.startTime,
      endTime: searchForm.value.endTime
    }

    const data = await CourseDataApi.getCourseDetail(params)
    tableData.value = data.list
    total.value = data.total

    // 数据已经通过后端筛选，前端不需要再次筛选
    filteredTableData.value = tableData.value
  } catch (error) {
    console.error('获取表格数据失败:', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    filteredTableData.value = []
    total.value = 0
  }
}

// 导出Excel
const handleExport = async () => {
  try {
    ElMessage.info('正在生成导出文件，请稍候...')
    const params = {
      ...searchForm.value
    }
    const res = await CourseDataApi.exportCourseDetail(params)
    if (res instanceof Blob) {
      const fileName = `线上课程明细数据_${new Date().getTime()}.xlsx`
      const blobUrl = window.URL.createObjectURL(res)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      ElMessage.success('线上课程明细数据导出成功')
    }
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出线上课程明细数据失败')
  }
}

const handleDetail = (row: any) => {
  if (!row.id) {
    ElMessage.error('课程ID不存在，无法查看详情')
    return
  }
  const courseId = row.id
  router.push({
    path: '/data/trainData/onlineDetailData',
    query: {
      type: 'detail',
      id: courseId,
      courseName: row.title
    }
  })
}

const handleEvaluation = (row: any) => {
  if (!row.id) {
    ElMessage.error('课程ID不存在，无法查看评价')
    return
  }
  const courseId = row.id
  router.push({
    path: '/data/trainData/onlineDetailData',
    query: {
      type: 'evaluation',
      id: courseId
    }
  })
}

onMounted(() => {
  getTableData()
  getDeptList()
  getCategoryList()
})
</script>

<style lang="scss" scoped>
.overview-container {
  padding: 20px;
  background-color: #f5f5f5;
}

.detail-search {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.course-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;

  .course-image {
    width: 100px;
    height: 60px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
  }

  .image-error {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    color: #909399;
    font-size: 20px;
  }

  .course-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .course-title {
      font-size: 14px;
      color: #303133;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .course-stats {
      display: flex;
      align-items: center;
      gap: 16px;
      color: #909399;
      font-size: 12px;

      span {
        display: inline-flex;
        align-items: center;
        gap: 4px;

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.white-block {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-form {
  .el-form {
    padding: 20px;
    background: #fafafa;
  }
}
</style>
