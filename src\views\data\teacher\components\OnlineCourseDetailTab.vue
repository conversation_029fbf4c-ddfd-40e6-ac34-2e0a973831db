<template>
  <div class="online-course-detail-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <span class="label">课程创建时间</span>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DDTHH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 360px"
            @change="handleDateChange"
          />
        </div>
        <div class="filter-item">
          <span class="label">讲师部门筛选</span>
          <dept-tree-select
            v-model="queryParams.departmentId"
            placeholder="请选择部门"
            style="width: 240px"
          />
        </div>
        <!-- <div class="filter-item">
          <span class="label">讲师岗位筛选</span>
          <post-tree-select
            v-model="queryParams.positionId"
            placeholder="所有岗位"
            style="width: 180px"
          />
        </div> -->
        <div class="filter-item">
          <span class="label">模糊查询</span>
          <el-select v-model="queryParams.searchField" placeholder="讲师姓名" style="width: 120px">
            <el-option label="讲师姓名" value="lecturerName" />
            <el-option label="课程名称" value="courseName" />
          </el-select>
          <el-input
            v-model="queryParams.keyword"
            placeholder="关键词"
            clearable
            @keyup.enter="handleSearch"
            style="width: 180px; margin-left: 8px"
          />
        </div>
      </div>
      <div class="filter-row">
        <div class="filter-item">
          <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
          <el-button @click="handleReset" style="margin-left: 10px">重置</el-button>
          <el-button type="primary" plain style="margin-left: 10px" @click="handleExport">
            导出结果
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="courseList"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column label="课程名称" prop="courseName" min-width="180" />
      <!-- <el-table-column label="课程编号" prop="courseCode" min-width="100" align="center" /> -->
      <el-table-column label="讲师姓名" prop="lecturerName" min-width="120">
        <template #default="{ row }">
          {{ row.lecturerName || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="讲师用户名" prop="lecturerUsername" min-width="120" align="center" />
      <el-table-column label="讲师等级" prop="lecturerLevel" min-width="100" align="center" />
      <el-table-column label="讲师所属部门" prop="lecturerDepartment" min-width="180" />
      <el-table-column label="课程创建时间" prop="createTime" min-width="150" align="center">
        <template #default="{ row }">
          {{ row.createTime ? formatDate(row.createTime) : '--' }}
        </template>
      </el-table-column>
      <el-table-column label="课程分类" prop="courseCategory" min-width="100" align="center" />
      <el-table-column label="课程加入人数" prop="joinCount" min-width="100" align="center" />
      <el-table-column label="课程完成人数" prop="completeCount" min-width="100" align="center" />
      <el-table-column
        label="累计课程学习时长(小时)"
        prop="learningDuration"
        min-width="100"
        align="center"
      />
      <el-table-column
        label="课程人均学习时长(小时)"
        prop="avgLearningDuration"
        min-width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ row.avgLearningDuration ? row.avgLearningDuration.toFixed(2) : 0 }}
        </template>
      </el-table-column>
      <el-table-column label="课程评价" prop="courseRating" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.courseRating ? row.courseRating.toFixed(2) : '--' }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getOnlineCourseDetailList,
  exportOnlineCourseDetailList
} from '@/api/data-manage/teacher/teacher'
import { formatDate } from '@/utils/formatTime'

interface QueryParams {
  departmentId: number | number[] | null
  positionId: string | number | null
  searchField: string
  keyword: string
  pageNo: number
  pageSize: number
  beginTime: string
  endTime: string
}

const queryParams = reactive<QueryParams>({
  departmentId: null as number | number[] | null,
  positionId: null as string | number | null,
  searchField: 'lecturerName',
  keyword: '',
  beginTime: '',
  endTime: '',
  pageNo: 1,
  pageSize: 10
})

const total = ref(0)
const loading = ref(false)

const courseList = ref<any[]>([])

const dateRange = ref<[string, string] | undefined>(undefined)

const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.beginTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  } else {
    queryParams.beginTime = ''
    queryParams.endTime = ''
  }
}

const handleSearch = async () => {
  loading.value = true
  console.log('发起搜索，当前查询参数:', JSON.parse(JSON.stringify(queryParams)))

  try {
    const params = {
      departmentId: queryParams.departmentId,
      positionId: queryParams.positionId,
      beginTime: queryParams.beginTime,
      endTime: queryParams.endTime,
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      ...(queryParams.keyword &&
        queryParams.searchField === 'lecturerName' && { lecturerName: queryParams.keyword }),
      ...(queryParams.keyword &&
        queryParams.searchField === 'courseName' && { courseName: queryParams.keyword })
    }

    // 过滤掉undefined和null值
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    console.log('最终发送给 API 的参数:', params)

    const response = await getOnlineCourseDetailList(params as any)
    console.log('API 响应:', response)

    if (response && response && response.list && Array.isArray(response.list)) {
      courseList.value = response.list
      total.value = response.total || response.list.length
      console.log(`数据加载成功，共 ${total.value} 条记录`)
    } else {
      console.warn(
        'API 响应结构不符合预期 { data: { list: [], total: 0 } }，请检查！实际响应:',
        response
      )
      courseList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取线上课程详情列表失败:', error)
    ElMessage.error({ message: '查询失败，请稍后重试' })
    courseList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleExport = async () => {
  try {
    const params = {
      departmentId: queryParams.departmentId,
      positionId: queryParams.positionId,
      beginTime: queryParams.beginTime,
      endTime: queryParams.endTime,
      ...(queryParams.keyword &&
        queryParams.searchField === 'lecturerName' && { lecturerName: queryParams.keyword }),
      ...(queryParams.keyword &&
        queryParams.searchField === 'courseName' && { courseName: queryParams.keyword })
    }

    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    ElMessage.info({ message: '正在导出数据...' })
    await exportOnlineCourseDetailList(params as any)
  } catch (error) {
    console.error('导出线上课程详情列表失败:', error)
    ElMessage.error({ message: '导出失败，请稍后重试' })
  }
}

const handleReset = () => {
  Object.assign(queryParams, {
    departmentId: null as number | number[] | null,
    positionId: null as string | number | null,
    searchField: 'lecturerName',
    keyword: '',
    beginTime: '',
    endTime: '',
    pageNo: 1,
    pageSize: 10
  })
  dateRange.value = undefined
  handleSearch()
}

const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  queryParams.pageNo = 1 // 重置到第一页
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  handleSearch()
}

// 暴露 getList 方法
defineExpose({
  getList: handleSearch
})

onMounted(() => {
  console.log('组件挂载，设置默认时间并首次查询')
  handleSearch()
})
</script>

<style lang="scss" scoped>
.online-course-detail-tab {
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
    background-color: #f5f7fa;
    padding: 16px;
    font-size: 14px;
    border-radius: 4px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 5px;

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;

        .label {
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .user-avatar {
      background-color: #dce2f1;
      margin-right: 8px;
      color: #5a8def;
      font-size: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-name {
      font-weight: 500;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

:deep(.el-table__header) {
  th {
    font-weight: 600;
  }
}

:deep(.el-button.is-link) {
  padding: 0 5px;
  height: auto;
  font-size: 13px;
}
</style>
