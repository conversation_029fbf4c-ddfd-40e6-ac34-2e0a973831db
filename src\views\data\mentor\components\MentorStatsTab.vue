<template>
  <div class="mentor-stats-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <span class="label">导师查询</span>
          <el-input
            v-model="queryParams.mentorSearch"
            placeholder="搜索导师名"
            style="width: 220px"
          />
        </div>
        <div class="filter-item">
          <span class="label">所属部门</span>
          <dept-tree-select
            v-model="queryParams.deptId"
            placeholder="请选部门"
            style="width: 220px"
          />
        </div>
        <div class="filter-item">
          <span class="label">所属岗位</span>
          <post-tree-select
            v-model="queryParams.postId"
            placeholder="请选岗位"
            style="width: 220px"
          />
        </div>
        <div class="filter-item">
          <span class="label">加入项目时间</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="—"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 240px"
          />
        </div>
      </div>
      <div class="filter-row">
        <div class="filter-item">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button style="margin-left: 8px" @click="handleExport">导出</el-button>
          <el-button style="margin-left: 8px" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="mentorList"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column label="导师" min-width="180">
        <template #default="{ row }">
          <div class="mentor-info">
            <el-avatar :size="40" :src="row.avatar" class="user-avatar">
              {{ row.nickname ? row.nickname.substr(0, 1) : '?' }}
            </el-avatar>
            <div class="mentor-name">{{ row.nickname }}</div>
          </div>
          <!-- <div class="mentor-username">{{ row.nickname }}</div> -->
        </template>
      </el-table-column>
      <el-table-column label="所属部门" prop="deptName" min-width="240" />
      <el-table-column label="所属岗位" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.postNames ? row.postNames.join('/') : '' }}
        </template>
      </el-table-column>
      <el-table-column label="角色" width="100" align="center">
        <template #default="{ row }">
          {{ row.roleNames ? row.roleNames.join('/') : '' }}
        </template>
      </el-table-column>
      <el-table-column label="带教人次" prop="mentoredStudentCount" width="120" align="center" />
      <el-table-column
        label="其中培训人次"
        prop="graduatedStudentCount"
        width="120"
        align="center"
      />
      <el-table-column
        label="其中项目完成人次"
        prop="completedTrainingCount"
        width="150"
        align="center"
      />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { MentorApi } from '@/api/data-manage/mentor/mentor'
// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 定义导师数据接口
interface MentorData {
  id: number
  userId: number
  nickname: string
  avatar?: string
  deptId: number
  deptName: string
  postLevelId: number
  postLevelName: string
  mentoredStudentCount: number
  graduatedStudentCount: number
  completedTrainingCount: number
}

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  mentorSearch: '',
  deptId: undefined as string | undefined,
  postId: undefined as string | undefined,
  startTime: undefined as string | undefined,
  endTime: undefined as string | undefined
})

// 页面状态
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const mentorList = ref<MentorData[]>([])

// 加载导师数据
const loadMentorData = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      pageNo: currentPage.value,
      pageSize: pageSize.value
    }

    const res = await MentorApi.getMentorStatisticPage(params)
    console.log(res)
    if (res.list) {
      mentorList.value = res.list || []
      total.value = res.total || 0
    } else {
      mentorList.value = []
      total.value = 0
      ElMessage.error(res.msg || '获取导师数据失败')
    }
  } catch (error) {
    console.error('加载导师数据失败:', error)
    ElMessage.error('获取导师数据失败')
    mentorList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索方法
const handleSearch = () => {
  currentPage.value = 1
  // 处理日期
  if (dateRange.value && dateRange.value.length === 2) {
    // 转换为毫秒时间戳字符串
    queryParams.startTime = new Date(dateRange.value[0]).getTime().toString()
    queryParams.endTime = new Date(dateRange.value[1]).getTime().toString()
  } else {
    queryParams.startTime = undefined
    queryParams.endTime = undefined
  }
  loadMentorData()
}

// 导出结果
const handleExport = async () => {
  try {
    const params = {
      // ...queryParams
    }

    // 如果有日期范围，处理日期
    if (dateRange.value && dateRange.value.length === 2) {
      params.startTime = new Date(dateRange.value[0]).getTime().toString()
      params.endTime = new Date(dateRange.value[1]).getTime().toString()
    }

    await MentorApi.exportMentorStatistic(params)
    ElMessage.success('导师统计数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出导师统计数据失败')
  }
}

// 重置筛选
const handleReset = () => {
  queryParams.mentorSearch = ''
  queryParams.deptId = undefined
  queryParams.postId = undefined
  queryParams.startTime = undefined
  queryParams.endTime = undefined
  dateRange.value = null
  handleSearch()
}

// 分页方法
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadMentorData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadMentorData()
}

defineExpose({ loadMentorData })
onMounted(() => {
  loadMentorData()
})
</script>

<style lang="scss" scoped>
.mentor-stats-tab {
  .filter-section {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;
    background-color: #f5f7fa;
    padding: 16px;
    font-size: 14px;
    border-radius: 4px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;

        .label {
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
  }

  .mentor-info {
    display: flex;
    align-items: center;

    .user-avatar {
      background-color: #dce2f1;
      margin-right: 8px;
      color: #5a8def;
      font-size: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mentor-name {
      font-weight: 500;
    }
  }

  .mentor-username {
    margin-top: 5px;
    color: #909399;
    font-size: 12px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

:deep(.el-table__header) {
  th {
    font-weight: 600;
  }
}
</style>
