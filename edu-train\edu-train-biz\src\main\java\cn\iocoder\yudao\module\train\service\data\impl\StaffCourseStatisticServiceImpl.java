package cn.iocoder.yudao.module.train.service.data.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.util.DeptUtil;
import cn.iocoder.yudao.framework.util.UserUtil;
import cn.iocoder.yudao.module.cms.api.question.QuestionApi;
import cn.iocoder.yudao.module.cms.api.question.dto.CourseQuestionAnswerCountDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.UserDeptPostInfoRespDTO;
import cn.iocoder.yudao.module.system.api.dept.PostApi;
import cn.iocoder.yudao.module.system.api.dept.dto.PostRespDTO;
import cn.iocoder.yudao.module.train.common.enums.CourseUserTypeEnum;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.*;
import cn.iocoder.yudao.module.train.dao.course.CourseHoursFlowMapper;
import cn.iocoder.yudao.module.train.dao.course.CourseMapper;
import cn.iocoder.yudao.module.train.model.course.po.CourseDO;
import cn.iocoder.yudao.module.train.model.course.po.CourseHoursFlowDO;
import cn.iocoder.yudao.module.train.model.course.po.CourseManagementDO;
import cn.iocoder.yudao.module.train.model.data.req.StaffOnlineCourseReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOnlineCourseDetailReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffProjectStatisticReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffProjectDetailReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOfflineCourseReq;
import cn.iocoder.yudao.module.train.model.data.req.StaffOfflineClassReq;
import cn.iocoder.yudao.module.train.model.data.resp.*;
import cn.iocoder.yudao.module.train.service.course.ICourseManagementService;
import cn.iocoder.yudao.module.train.service.data.IStaffCourseStatisticService;
import cn.iocoder.yudao.module.train.service.data.UserDataService;
import cn.iocoder.yudao.module.train.trainingproject.project.entity.TrainStudent;
import cn.iocoder.yudao.module.train.trainingproject.project.mapper.TrainStudentMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Validated
public class StaffCourseStatisticServiceImpl implements IStaffCourseStatisticService {


    @Resource
    private CourseMapper courseMapper;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private CourseHoursFlowMapper courseHoursFlowMapper;
    @Resource
    private PostApi postApi;

    @Resource
    private ICourseManagementService managementService;
    @Resource
    private QuestionApi questionApi;
    @Resource
    private TrainStudentMapper trainStudentMapper;
    @Resource
    private UserDataService userDataService;

    @Override
    public PageResult<StaffOnlineCourseResp> getStaffOnlineCoursePage(StaffOnlineCourseReq pageReq) {
        Page<StaffOnlineCourseResp> page = MyBatisUtils.buildPage(pageReq, null);
        Page<StaffOnlineCourseResp> statisticPage = courseMapper.findStaffOnlineCourseRespPage(page, pageReq);
        for (StaffOnlineCourseResp record : statisticPage.getRecords()) {
            record.setJoinCount(managementService.getStudentCountByCourseId(record.getCourseId()));
            // TODO 处理完成人数、完课率业务逻辑
        }
        return new PageResult<>(statisticPage.getRecords(), statisticPage.getTotal());
    }

    @Override
    public PageResult<StaffOnlineCourseDetailResp> getStaffOnlineCourseDetailPage(StaffOnlineCourseDetailReq pageReq) {
        Page<StaffOnlineCourseDetailResp> page = MyBatisUtils.buildPage(pageReq, null);
        Page<StaffOnlineCourseDetailResp> statisticPage = courseMapper.findStaffOnlineCourseDetailRespPage(page, pageReq);
        for (StaffOnlineCourseDetailResp record : statisticPage.getRecords()) {
            // 安全设置部门名称
            if (record.getDeptId() != null) {
                try {
                    record.setDeptName(DeptUtil.getDept(record.getDeptId()).getName());
                } catch (Exception e) {
                    record.setDeptName("");
                }
            }
            
            // 获取讲师数据
            if (record.getCourseId() != null) {
                try {
                    List<CourseManagementDO> managementList = managementService.lambdaQuery()
                            .eq(CourseManagementDO::getCourseId, record.getCourseId())
                            .eq(CourseManagementDO::getUserType, CourseUserTypeEnum.TEACHER.getCode())
                            .list();
                    if (!managementList.isEmpty()) {
                        CourseManagementDO managementDO = managementList.get(0);
                        record.setCourseTeacherId(managementDO.getUserId());
                        record.setCourseTeacherName(managementDO.getUserName());
                        
                        // 尝试获取用户昵称
                        try {
                            if (managementDO.getUserId() != null) {
                                record.setCourseTeacherName(UserUtil.getUser(managementDO.getUserId()).getNickname());
                            }
                        } catch (Exception e) {
                            // 如果获取昵称失败，保持原有的用户名
                        }
                    }
                } catch (Exception e) {
                    // 如果获取讲师信息失败，设置默认值
                    record.setCourseTeacherId(null);
                    record.setCourseTeacherName("");
                }
            }

            // TODO 课程学习时长、加入时间、完成时间业务需完善
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<StaffOnlineCourseResp> exportStaffOnlineCourseList(StaffOnlineCourseReq exportReq) {
        PageResult<StaffOnlineCourseResp> pageResult = this.getStaffOnlineCoursePage(exportReq);
        return pageResult.getList();
    }

    @Override
    public List<StaffOnlineCourseDetailResp> exportStaffOnlineCourseDetailList(StaffOnlineCourseDetailReq exportReq) {
        PageResult<StaffOnlineCourseDetailResp> pageResult = this.getStaffOnlineCourseDetailPage(exportReq);
        return pageResult.getList();
    }


    @Override
    public PageResult<StaffProjectStatisticResp> getStaffProjectStatisticPage(StaffProjectStatisticReq pageReq) {
        return null;
    }
/* 
 */    @Override
    public List<StaffProjectStatisticResp> exportStaffProjectStatisticList(StaffProjectStatisticReq exportReq) {
        // 执行导出操作
        return Collections.emptyList(); // 示例实现，实际需根据业务逻辑查询数据
    }
/* 
 */    @Override
    public PageResult<StaffProjectDetailResp> getStaffProjectDetailPage(StaffProjectDetailReq pageReq) {
        // TODO: 实现获取员工培训项目明细分页的逻辑
        // 这里只是一个空实现，具体实现需要根据业务需求进行开发
        return new PageResult<>(Collections.emptyList(), 0L);
    }

    @Override
    public List<StaffProjectDetailResp> exportStaffProjectDetailList(StaffProjectDetailReq exportReq) {
        // TODO: 实现导出员工培训项目明细数据的逻辑
        // 这里只是一个空实现，具体实现需要根据业务需求进行开发
        return Collections.emptyList();
    }

    @Override
    public PageResult<StaffOfflineCourseResp> getStaffOfflineCoursePage(StaffOfflineCourseReq pageReq) {
        Page<StaffOfflineCourseResp> page = MyBatisUtils.buildPage(pageReq, null);
        Page<StaffOfflineCourseResp> statisticPage = courseMapper.findStaffOfflineCourseRespPage(page, pageReq);
        for (StaffOfflineCourseResp record : statisticPage.getRecords()) {
            // 设置部门名称
            if (record.getDeptId() != null) {
                record.setDeptName(DeptUtil.getDept(record.getDeptId()).getName());
            }

            // 获取讲师数据
            if (record.getCourseId() != null) {
                CourseManagementDO managementDO = managementService.lambdaQuery()
                        .eq(CourseManagementDO::getCourseId, record.getCourseId())
                        .eq(CourseManagementDO::getUserType, CourseUserTypeEnum.TEACHER.getCode())
                        .oneOpt()
                        .orElse(null);
                Optional.ofNullable(managementDO).ifPresent(DO -> {
                    record.setCourseTeacherId(managementDO.getUserId());
                    record.setCourseTeacherName(managementDO.getUserName());
                });
            }

            // TODO 场次类型、场次名称、课程学时、上课时间、课程讲师、上课地点业务需完善
        }
        return new PageResult<>(statisticPage.getRecords(), statisticPage.getTotal());
    }

    @Override
    public List<StaffOfflineCourseResp> exportStaffOfflineCourseList(StaffOfflineCourseReq exportReq) {
        PageResult<StaffOfflineCourseResp> pageResult = this.getStaffOfflineCoursePage(exportReq);
        return pageResult.getList();
    }

    @Override
    public PageResult<StaffOfflineClassResp> getStaffOfflineClassPage(StaffOfflineClassReq pageReq) {
        Page<StaffOfflineClassResp> page = MyBatisUtils.buildPage(pageReq, null);
        Page<StaffOfflineClassResp> statisticPage = courseMapper.findStaffOfflineClassRespPage(page, pageReq);
        for (StaffOfflineClassResp record : statisticPage.getRecords()) {
            // 设置部门名称
            if (record.getDeptId() != null) {
                record.setDeptName(DeptUtil.getDept(record.getDeptId()).getName());
            }

            // 获取讲师数据
            if (record.getCourseId() != null) {
                CourseManagementDO managementDO = managementService.lambdaQuery()
                        .eq(CourseManagementDO::getCourseId, record.getCourseId())
                        .eq(CourseManagementDO::getUserType, CourseUserTypeEnum.TEACHER.getCode())
                        .oneOpt()
                        .orElse(null);
                Optional.ofNullable(managementDO).ifPresent(DO -> {
                    record.setCourseTeacherId(managementDO.getUserId());
                    record.setCourseTeacherName(managementDO.getUserName());
                });
            }

            // TODO 上课时间、上课地点、上课讲师、签到情况、考核结果、证书领取情况业务需完善
        }
        return new PageResult<>(statisticPage.getRecords(), statisticPage.getTotal());
    }

    @Override
    public List<StaffOfflineClassResp> exportStaffOfflineClassList(StaffOfflineClassReq exportReq) {
        PageResult<StaffOfflineClassResp> pageResult = this.getStaffOfflineClassPage(exportReq);
        return pageResult.getList();
    }

    @Override
    public UserCourseStudyTimeStatisticResp getUserCourseStudyTimeStatistics(LocalDate startTime, LocalDate endTime, Long deptId, Long postId) {
        // 1. 查询符合条件的用户ID

        List<Long> userIds = new ArrayList<>();// 通过部门和岗位筛选用户ID，建议调用AdminUserApi
        List<Long> filetDpt = adminUserApi.getUserListByDeptIds(Collections.singleton(deptId)).getCheckedData().stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
        List<Long> filetPost = adminUserApi.getUserListByPostIds(Collections.singleton(postId)).getCheckedData().stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
        if (!filetDpt.isEmpty()) {
            userIds = filetDpt;

        }
        if (!filetPost.isEmpty()) {
            userIds = filetPost;
        }
        if (!filetDpt.isEmpty() && !filetPost.isEmpty()) {
            userIds = filetDpt.stream().filter(filetPost::contains).collect(Collectors.toList());
        }
        // 2. 查询学时流水表
        List<CourseHoursFlowDO> flows = courseHoursFlowMapper.selectList(
                new LambdaQueryWrapper<CourseHoursFlowDO>()
                        .in(!userIds.isEmpty(), CourseHoursFlowDO::getUserId, userIds)
                        .ge(CourseHoursFlowDO::getUpdateTime, startTime)
                        .le(CourseHoursFlowDO::getUpdateTime, endTime)
        );

        // 3. 统计线上和线下总学习时长
        UserCourseStudyTimeStatisticResp userCourseStudyTimeStatisticResp = new UserCourseStudyTimeStatisticResp();
        userCourseStudyTimeStatisticResp.setTotalStudyOnlineCourseTime(flows.stream().filter(it -> it.getTargetType() == 1 || it.getTargetType() == 2 || it.getTargetType() == 4).mapToInt(CourseHoursFlowDO::getChangeValue).sum());
        userCourseStudyTimeStatisticResp.setTotalStudyOfflineCourseTime(flows.stream().filter(it -> it.getTargetType() == 3).mapToInt(CourseHoursFlowDO::getChangeValue).sum());
        userCourseStudyTimeStatisticResp.setUserCount(flows.size());
        if (flows.isEmpty()) {
            userCourseStudyTimeStatisticResp.setAvgOnlineCourseStudyTime(0.0);
            userCourseStudyTimeStatisticResp.setAvgOfflineCourseStudyTime(0.0);

        } else {
            userCourseStudyTimeStatisticResp.setAvgOnlineCourseStudyTime((double) (userCourseStudyTimeStatisticResp.getTotalStudyOnlineCourseTime() / userCourseStudyTimeStatisticResp.getUserCount()));
            userCourseStudyTimeStatisticResp.setAvgOfflineCourseStudyTime((double) (userCourseStudyTimeStatisticResp.getTotalStudyOfflineCourseTime() / userCourseStudyTimeStatisticResp.getUserCount()));
        }
        return userCourseStudyTimeStatisticResp;
    }

    @Override
    public List<StaffKnowledgeBehaviorStatisticResp> getStaffKnowledgeBehaviorStatistics(LocalDate startTime, LocalDate endTime, Long deptId, Long postId) {
        return courseMapper.selectStaffKnowledgeBehaviorStatistics(startTime, endTime, deptId, postId);
    }

    @Override
    public DeptQuestionAnswerStatisticResp getDeptQuestionAnswerStatistics(Long deptId, LocalDate startTime, LocalDate endTime, Long postId) {
        // 1. 查询符合条件的用户ID
        List<Long> userIds = adminUserApi.getUserIdsByDeptAndPost(deptId, postId).getCheckedData();

        if (userIds.isEmpty()) {
            return new DeptQuestionAnswerStatisticResp(0, 0, 0);
        }
        // 2. 查找这些员工创建的所有课程id
        // 2. 查询学时流水表
        List<CourseHoursFlowDO> flows = courseHoursFlowMapper.selectList(
                new LambdaQueryWrapper<CourseHoursFlowDO>()
                        .in(!userIds.isEmpty(), CourseHoursFlowDO::getUserId, userIds)
                        .ge(CourseHoursFlowDO::getUpdateTime, startTime)
                        .le(CourseHoursFlowDO::getUpdateTime, endTime)
        );
        //真正学习的人
        userIds = flows.stream().map(CourseHoursFlowDO::getUserId).distinct().collect(Collectors.toList());
        //将学时表中的课程id提取
        List<Long> courseIds = flows.stream().map(CourseHoursFlowDO::getTargetId).distinct().collect(Collectors.toList());
        if (courseIds.isEmpty()) {
            return new DeptQuestionAnswerStatisticResp(0, 0, 0);
        }
        // 3. 调用QuestionApi
        List<CourseQuestionAnswerCountDto> list = questionApi.selectCourseQuestionAndAnswerCountByCourseIds(courseIds, userIds).getCheckedData();
        int totalQuestion = 0, totalAnswer = 0, totalLike = 0;
        for (CourseQuestionAnswerCountDto dto : list) {
            totalQuestion += dto.getQuestionCount() != null ? dto.getQuestionCount() : 0;
            totalAnswer += dto.getAnswerCount() != null ? dto.getAnswerCount() : 0;
            totalLike += dto.getLikeCount() != null ? dto.getLikeCount() : 0;
        }
        return new DeptQuestionAnswerStatisticResp(totalQuestion, totalAnswer, totalLike);
    }

    @Override
    public List<StaffTopicStatisticResp> getStaffTopicStatistics(Long deptId, LocalDate startTime, LocalDate endTime, Long postId) {
        // 1. 查询符合条件的用户ID
        List<Long> userIds = adminUserApi.getUserIdsByDeptAndPost(deptId, postId).getCheckedData();

        if (userIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<StaffTopicStatisticResp> staffTopicStatisticResps = courseMapper.selectStaffTopicStatistics(userIds, startTime, endTime);
        return staffTopicStatisticResps;
    }

    @Override
    public PageResult<StaffComplexStatisticResp> getStaffComplexStatistics(StaffComplexStatisticReq req) {
        // 1. 查询所有符合条件的用户ID
        PageResult<Long> userIdsByComplexCondition = adminUserApi.getUserIdsByComplexCondition(
                req.getDeptId(), req.getPostId(), req.getBanned(),
                req.getEntryTimeOption(), req.getEntryDays(),
                req.getPreciseField(), req.getKeyword(),
                req.getNickName(), req.getUserName(),
                req.getPageNo(), req.getPageSize()
        );
        List<Long> userIds = userIdsByComplexCondition.getList();
        if (userIds.isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 2. 查询用户基本信息
        List<AdminUserRespDTO> userList = adminUserApi.getUserList(userIds).getCheckedData();
        Map<Long, AdminUserRespDTO> userMap = userList.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, u -> u));

        // 3. 查询课程统计
        Map<Long, UserCourseStatisticPageResp> courseStatMap = batchUserCourseStatistics(userIds, req.getStartDate(), req.getEndDate());

        // 4. 查询培训项目统计
        Map<Long, Integer> projectStatMap = batchUserTrainProjectJoinCount(userIds, req.getStartDate(), req.getEndDate());

        // 5. 组装结果
        List<StaffComplexStatisticResp> resultList = new ArrayList<>();
        for (Long userId : userIds) {
            StaffComplexStatisticResp resp = new StaffComplexStatisticResp();
            resp.setUserId(userId);

            // 用户基本信息
            AdminUserRespDTO user = userMap.get(userId);
            if (user != null) {
                resp.setNickname(user.getNickname());

                // 获取用户部门岗位信息，添加空值检查
                try {
                    UserDeptPostInfoRespDTO deptPostInfo = adminUserApi.getUserDeptPostInfo(userId).getCheckedData();
                    if (deptPostInfo != null) {
                        resp.setPostName(deptPostInfo.getPostName());
                        resp.setDeptName(deptPostInfo.getDeptName());
                    } else {
                        // 如果获取不到部门岗位信息，设置默认值
                        resp.setPostName("");
                        resp.setDeptName("");
                    }
                } catch (Exception e) {
                    // 如果获取部门岗位信息失败，设置默认值
                    resp.setPostName("");
                    resp.setDeptName("");
                }

                // 获取用户岗位列表
                try {
                    if (user.getPostIds() != null && !user.getPostIds().isEmpty()) {
                        List<PostRespDTO> postList = postApi.getPostList(user.getPostIds()).getCheckedData();
                        if (postList != null && !postList.isEmpty()) {
                            List<String> postNames = postList.stream()
                                    .map(PostRespDTO::getName)
                                    .collect(Collectors.toList());
                            resp.setPostNames(postNames);
                        } else {
                            resp.setPostNames(Collections.emptyList());
                        }
                    } else {
                        resp.setPostNames(Collections.emptyList());
                    }
                } catch (Exception e) {
                    // 如果获取岗位列表失败，设置空列表
                    resp.setPostNames(Collections.emptyList());
                }
            }

            // 课程统计
            UserCourseStatisticPageResp stat = courseStatMap.get(userId);
            if (stat != null) {
                resp.setOnlineNewStudyTime(stat.getOnlineNewStudyTime());
                resp.setOnlineTotalStudyTime(stat.getOnlineTotalStudyTime());
                resp.setOnlineCourseCount(stat.getOnlineCourseCount());
                resp.setOnlineCourseCompleteCount(stat.getOnlineCourseCompleteCount());

                resp.setOfflineNewStudyTime(stat.getOfflineNewStudyTime());
                resp.setOfflineTotalStudyTime(stat.getOfflineTotalStudyTime());
                resp.setOfflineCourseCount(stat.getOfflineCourseCount());
                resp.setOfflineCourseCompleteCount(stat.getOfflineCourseCompleteCount());

                resp.setTopicNewStudyTime(stat.getTopicNewStudyTime());
                resp.setTopicTotalStudyTime(stat.getTopicTotalStudyTime());
                resp.setTopicCourseCount(stat.getTopicCourseCount());
                resp.setTopicCourseCompleteCount(stat.getTopicCourseCompleteCount());
            }

            // 培训项目
            resp.setTrainProjectJoinCount(projectStatMap.getOrDefault(userId, 0));

            resultList.add(resp);
        }

        // 6. 返回分页结果
        return new PageResult<>(resultList, userIdsByComplexCondition.getTotal());
    }

    public Map<Long, UserCourseStatisticPageResp> batchUserCourseStatistics(
            List<Long> userIds,
            LocalDate startTime,
            LocalDate endTime
    ) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 1. 查询所有课程，按类型分组
        List<CourseDO> allCourses = courseMapper.selectList(null);
        Map<Integer, List<CourseDO>> courseTypeMap = allCourses.stream()
                .collect(Collectors.groupingBy(CourseDO::getCourseType));
        // 修复：过滤掉courseHours为null的课程，避免NullPointerException
        Map<Long, Integer> courseHoursMap = allCourses.stream()
                .filter(course -> course.getCourseHours() != null)
                .collect(Collectors.toMap(CourseDO::getId, CourseDO::getCourseHours));

        // 2. 统计各类型课程ID集合
        Set<Long> onlineCourseIds = courseTypeMap.getOrDefault(1, Collections.emptyList())
                .stream().map(CourseDO::getId).collect(Collectors.toSet());
        Set<Long> offlineCourseIds = courseTypeMap.getOrDefault(3, Collections.emptyList())
                .stream().map(CourseDO::getId).collect(Collectors.toSet());
        Set<Long> topicCourseIds = courseTypeMap.getOrDefault(2, Collections.emptyList())
                .stream().map(CourseDO::getId).collect(Collectors.toSet());

        // 3. 查询所有用户在时间段内的学时流水（新增）
        LambdaQueryWrapper<CourseHoursFlowDO> flowWrapper = new LambdaQueryWrapper<>();
        flowWrapper.in(CourseHoursFlowDO::getUserId, userIds)
                .gt(CourseHoursFlowDO::getChangeValue, 0);
        if (startTime != null) flowWrapper.ge(CourseHoursFlowDO::getCreateTime, startTime);
        if (endTime != null) flowWrapper.le(CourseHoursFlowDO::getCreateTime, endTime);

        List<CourseHoursFlowDO> flowList = courseHoursFlowMapper.selectList(flowWrapper);

        // 4. 查询所有用户截止endTime的学时流水（总）
        LambdaQueryWrapper<CourseHoursFlowDO> totalWrapper = new LambdaQueryWrapper<>();
        totalWrapper.in(CourseHoursFlowDO::getUserId, userIds)
                .gt(CourseHoursFlowDO::getChangeValue, 0);
        if (endTime != null) totalWrapper.le(CourseHoursFlowDO::getCreateTime, endTime);
        List<CourseHoursFlowDO> totalFlowList = courseHoursFlowMapper.selectList(totalWrapper);

        // 5. 组装结果
        Map<Long, UserCourseStatisticPageResp> result = new HashMap<>();
        for (Long userId : userIds) {
            UserCourseStatisticPageResp vo = new UserCourseStatisticPageResp();

            // ========== 线上 ==========
            // 新增
            List<CourseHoursFlowDO> onlineFlows = flowList.stream()
                    .filter(f -> f.getUserId().equals(userId) && onlineCourseIds.contains(f.getTargetId()))
                    .collect(Collectors.toList());
            vo.setOnlineNewStudyTime(onlineFlows.stream().mapToInt(CourseHoursFlowDO::getChangeValue).sum());
            vo.setOnlineCourseCount((int) onlineFlows.stream().map(CourseHoursFlowDO::getTargetId).distinct().count());
            // 总
            List<CourseHoursFlowDO> onlineTotalFlows = totalFlowList.stream()
                    .filter(f -> f.getUserId().equals(userId) && onlineCourseIds.contains(f.getTargetId()))
                    .collect(Collectors.toList());
            vo.setOnlineTotalStudyTime(onlineTotalFlows.stream().mapToInt(CourseHoursFlowDO::getChangeValue).sum());
            vo.setOnlineCourseCompleteCount(getCompleteCount(onlineTotalFlows, courseHoursMap));

            // ========== 线下 ==========
            List<CourseHoursFlowDO> offlineFlows = flowList.stream()
                    .filter(f -> f.getUserId().equals(userId) && offlineCourseIds.contains(f.getTargetId()))
                    .collect(Collectors.toList());
            vo.setOfflineNewStudyTime(offlineFlows.stream().mapToInt(CourseHoursFlowDO::getChangeValue).sum());
            vo.setOfflineCourseCount((int) offlineFlows.stream().map(CourseHoursFlowDO::getTargetId).distinct().count());
            List<CourseHoursFlowDO> offlineTotalFlows = totalFlowList.stream()
                    .filter(f -> f.getUserId().equals(userId) && offlineCourseIds.contains(f.getTargetId()))
                    .collect(Collectors.toList());
            vo.setOfflineTotalStudyTime(offlineTotalFlows.stream().mapToInt(CourseHoursFlowDO::getChangeValue).sum());
            vo.setOfflineCourseCompleteCount(getCompleteCount(offlineTotalFlows, courseHoursMap));

            // ========== 专题 ==========
            List<CourseHoursFlowDO> topicFlows = flowList.stream()
                    .filter(f -> f.getUserId().equals(userId) && topicCourseIds.contains(f.getTargetId()))
                    .collect(Collectors.toList());
            vo.setTopicNewStudyTime(topicFlows.stream().mapToInt(CourseHoursFlowDO::getChangeValue).sum());
            vo.setTopicCourseCount((int) topicFlows.stream().map(CourseHoursFlowDO::getTargetId).distinct().count());
            List<CourseHoursFlowDO> topicTotalFlows = totalFlowList.stream()
                    .filter(f -> f.getUserId().equals(userId) && topicCourseIds.contains(f.getTargetId()))
                    .collect(Collectors.toList());
            vo.setTopicTotalStudyTime(topicTotalFlows.stream().mapToInt(CourseHoursFlowDO::getChangeValue).sum());
            vo.setTopicCourseCompleteCount(getCompleteCount(topicTotalFlows, courseHoursMap));

            result.put(userId, vo);
        }
        return result;
    }

    /**
     * 统计完成课程数（某用户某类型所有课程，累计学时>=课程学时即为完成）
     */
    private int getCompleteCount(List<CourseHoursFlowDO> flows, Map<Long, Integer> courseHoursMap) {
        Map<Long, Integer> courseTimeMap = flows.stream()
                .collect(Collectors.groupingBy(CourseHoursFlowDO::getTargetId,
                        Collectors.summingInt(CourseHoursFlowDO::getChangeValue)));
        int completeCount = 0;
        for (Map.Entry<Long, Integer> entry : courseTimeMap.entrySet()) {
            Integer required = courseHoursMap.get(entry.getKey());
            if (required != null && entry.getValue() >= required) {
                completeCount++;
            }
        }
        return completeCount;
    }
    public Map<Long, Integer> batchUserTrainProjectJoinCount(
            List<Long> userIds,
            LocalDate startTime,
            LocalDate endTime
    ) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<TrainStudent> wrapper = new LambdaQueryWrapper<TrainStudent>()
                .in(TrainStudent::getStudentId, userIds)
                .eq(TrainStudent::getSignupStatus, 1); // 只统计报名通过的

        if (startTime != null) {
            wrapper.ge(TrainStudent::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(TrainStudent::getCreateTime, endTime);
        }

        List<TrainStudent> studentList = trainStudentMapper.selectList(wrapper);

        // 统计每个用户参与的项目数（去重projectId）
        Map<Long, Set<Long>> userProjectSetMap = studentList.stream()
                .collect(Collectors.groupingBy(
                        TrainStudent::getStudentId,
                        Collectors.mapping(TrainStudent::getProjectId, Collectors.toSet())
                ));

        // 转为Map<Long, Integer>
        Map<Long, Integer> result = new HashMap<>();
        for (Long userId : userIds) {
            Set<Long> projectSet = userProjectSetMap.getOrDefault(userId, Collections.emptySet());
            result.put(userId, projectSet.size());
        }
        return result;
    }
    @Override
    public StaffKnowledgeBehaviorCountStatistic getStaffKnowledgeBehaviorCountStatistics(
            LocalDate startTime, LocalDate endTime, Long deptId, Long postId) {
        // 获取原始统计数据
        List<StaffKnowledgeBehaviorStatisticResp> statistics =
                this.getStaffKnowledgeBehaviorStatistics(startTime, endTime, deptId, postId);

        // 计算总数
        int totalLikeCount = 0;
        int totalFavoriteCount = 0;
        int totalSubscribeCount = 0;

        for (StaffKnowledgeBehaviorStatisticResp resp : statistics) {
            totalLikeCount += resp.getLikeCount() != null ? resp.getLikeCount() : 0;
            totalFavoriteCount += resp.getFavoriteCount() != null ? resp.getFavoriteCount() : 0;
            totalSubscribeCount += resp.getSubscribeCount() != null ? resp.getSubscribeCount() : 0;
        }

        // 计算用户数
        int userCount = statistics.size();

        // 构建返回对象
        StaffKnowledgeBehaviorCountStatistic statistic = new StaffKnowledgeBehaviorCountStatistic();
        statistic.setLikeCount(totalLikeCount);
        statistic.setFavoriteCount(totalFavoriteCount);
        statistic.setSubscribeCount(totalSubscribeCount);

        // 计算平均值
        statistic.setAverageLikeCount(userCount > 0 ? (double) totalLikeCount / userCount : 0.0);
        statistic.setAverageFavoriteCount(userCount > 0 ? (double) totalFavoriteCount / userCount : 0.0);
        statistic.setAverageSubscribeCount(userCount > 0 ? (double) totalSubscribeCount / userCount : 0.0);

        return statistic;
    }

    @Override
    public StaffTopicStatisticSummaryResp getStaffTopicStatisticSummary(
            Long deptId, Long postId, LocalDate startTime, LocalDate endTime) {
        // 获取原始统计数据
        List<StaffTopicStatisticResp> list =
                this.getStaffTopicStatistics(deptId, startTime, endTime, postId);

        // 计算总数
        int userCount = list.size();
        int totalTopicCount = 0;
        int totalTopicReplyCount = 0;
        int totalInfoReplyCount = 0;

        for (StaffTopicStatisticResp resp : list) {
            totalTopicCount += resp.getTopicCount() != null ? resp.getTopicCount() : 0;
            totalTopicReplyCount += resp.getTopicReplyCount() != null ? resp.getTopicReplyCount() : 0;
            totalInfoReplyCount += resp.getInfoReplyCount() != null ? resp.getInfoReplyCount() : 0;
        }

        // 构建返回对象
        StaffTopicStatisticSummaryResp summary = new StaffTopicStatisticSummaryResp();
        summary.setTotalTopicCount(totalTopicCount);
        summary.setTotalTopicReplyCount(totalTopicReplyCount);
        summary.setTotalInfoReplyCount(totalInfoReplyCount);
        summary.setUserCount(userCount);

        // 计算平均值
        summary.setAvgTopicCount(userCount > 0 ? (double) totalTopicCount / userCount : 0.0);
        summary.setAvgTopicReplyCount(userCount > 0 ? (double) totalTopicReplyCount / userCount : 0.0);
        summary.setAvgInfoReplyCount(userCount > 0 ? (double) totalInfoReplyCount / userCount : 0.0);

        return summary;
    }
    @Override
    public StaffInteractionStatisticRespVO getStaffInteractionStatistics(
            LocalDate startTime, LocalDate endTime, Long deptId, Long postId) {
        StaffInteractionStatisticRespVO respVO = new StaffInteractionStatisticRespVO();

        // 获取知识库行为统计
        respVO.setKnowledgeBehavior(getStaffKnowledgeBehaviorCountStatistics(
                startTime, endTime, deptId, postId));

        // 获取部门问答统计
        respVO.setQuestionAnswer(getDeptQuestionAnswerStatistics(
                deptId, startTime, endTime, postId));

        // 获取话题统计
        respVO.setTopic(getStaffTopicStatisticSummary(
                deptId, postId, startTime, endTime));

        return respVO;
    }
    @Override
    public StaffLearningParticipationRespVO getStaffLearningParticipationStatistics(
            LocalDate startTime, LocalDate endTime,
            Long deptId, Long postId, Long roleId, Integer employDaysGreaterThan) {
        StaffLearningParticipationRespVO respVO = new StaffLearningParticipationRespVO();

        // 获取课程学习时长统计
        respVO.setStudyTime(getUserCourseStudyTimeStatistics(
                startTime, endTime, deptId, postId));

        // 获取学习参与度统计
        respVO.setParticipation(userDataService.getLearnParticipationStatistics(
                startTime.atStartOfDay(), endTime.atStartOfDay(), deptId, postId, roleId, employDaysGreaterThan));

        return respVO;
    }

    @Override
    public List<StaffComplexStatisticResp> exportStaffComplexStatisticsList(StaffComplexStatisticReq req) {
        // 设置一个较大的页面大小来获取所有数据
        req.setPageNo(1);
        req.setPageSize(10000); // 设置一个足够大的数值

        PageResult<StaffComplexStatisticResp> pageResult = getStaffComplexStatistics(req);
        List<StaffComplexStatisticResp> resultList = pageResult.getList();

        // 为导出数据处理岗位列表字符串
        for (StaffComplexStatisticResp resp : resultList) {
            if (resp.getPostNames() != null && !resp.getPostNames().isEmpty()) {
                resp.setPostNamesStr(String.join(", ", resp.getPostNames()));
            } else {
                resp.setPostNamesStr("");
            }
        }

        return resultList;
    }
}