package cn.iocoder.yudao.module.train.model.data.req;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 员工学习明细统计分页 Request DTO
 */
@Schema(description = "员工学习明细统计分页Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StaffOnlineCourseDetailReq extends PageParam {

    @Schema(description = "学习时间区间-开始", example = "2023-10-01")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate beginTime;

    @Schema(description = "学习时间区间-结束", example = "2023-12-31")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @Schema(description = "部门编号")
    private Long deptId;

    @Schema(description = "员工工号或名称")
    private String keyword;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程类型")
    private Integer courseType;

    @Schema(description = "岗位编号")
    private Long postId;

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "姓名关键字")
    private String name;

    @Schema(description = "用户昵称模糊查询")
    private String nickName;

    @Schema(description = "用户名模糊查询")
    private String userName;

    @Schema(description = "课程分类ID")
    private Long categoryId;

    @Schema(description = "课程ID")
    private Long courseId;

} 