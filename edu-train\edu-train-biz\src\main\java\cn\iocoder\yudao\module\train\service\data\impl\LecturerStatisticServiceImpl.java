package cn.iocoder.yudao.module.train.service.data.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.PostApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.dept.dto.PostRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.CourseSummaryReqVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.CourseSummaryRespVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.CourseStatisticsReqVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.CourseStatisticsRespVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.LecturerDistributionRespVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.LecturerDetailReqVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.LecturerDetailRespVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.OnlineCourseDetailReqVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.OnlineCourseDetailRespVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.OfflineCourseDetailReqVO;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.OfflineCourseDetailRespVO;
import cn.iocoder.yudao.module.train.dao.courseknowledge.CourseCategoryMapper;
import cn.iocoder.yudao.module.train.model.courseknowledge.po.CourseCategoryDO;
import cn.iocoder.yudao.module.train.service.data.LecturerStatisticService;
import cn.iocoder.yudao.module.train.service.course.ICourseService;
import cn.iocoder.yudao.module.train.service.course.ICourseManagementService;
import cn.iocoder.yudao.module.train.service.implementtraining.service.ClassService;
import cn.iocoder.yudao.module.train.service.implementtraining.entity.ClassEntity;
import cn.iocoder.yudao.module.train.service.teachingfaculty.TrainLecturerService;
import cn.iocoder.yudao.module.train.service.teachingfaculty.TrainLecturerLevelService;
import cn.iocoder.yudao.module.train.service.teachingfaculty.TrainLecturerSpecialtyService;
import cn.iocoder.yudao.module.train.model.teachingfaculty.TrainLecturerDO;
import cn.iocoder.yudao.module.train.model.teachingfaculty.TrainLecturerLevelDO;
import cn.iocoder.yudao.module.train.model.teachingfaculty.TrainLecturerSpecialtyDO;
import cn.iocoder.yudao.module.train.model.course.po.CourseDO;
import cn.iocoder.yudao.module.train.model.course.po.CourseManagementDO;
import cn.iocoder.yudao.module.train.common.enums.course.CourseTypeEnum;
import cn.iocoder.yudao.module.train.common.enums.CourseUserTypeEnum;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.stream.Stream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 讲师数据统计 Service 实现类
 */
@Service
@Validated
public class LecturerStatisticServiceImpl implements LecturerStatisticService {
    
    @Resource
    private TrainLecturerService trainLecturerService;
    
    @Resource
    private TrainLecturerLevelService trainLecturerLevelService;
    
    @Resource
    private TrainLecturerSpecialtyService trainLecturerSpecialtyService;
    
    @Resource
    private ICourseService courseService;

    // 引入系统API
    @Resource
    private AdminUserApi adminUserApi;
    
    @Resource
    private DeptApi deptApi;
    
    @Resource
    private PostApi postApi;
    
    @Resource
    private ICourseManagementService courseManagementService;
    @Resource
    private CourseCategoryMapper courseCategoryMapper;
    @Resource
    private ClassService classService;
    
    @Override
    public Long getLecturerCount() {
        // 调用讲师服务获取已删除的讲师总数
        return trainLecturerService.lambdaQuery()
                .eq(TrainLecturerDO::getDeleted, false)
                .count();
    }
    
    @Override
    public List<LecturerDistributionRespVO> getSpecialtyDistribution() {
        // 1. 获取所有专业领域
        List<TrainLecturerSpecialtyDO> specialties = trainLecturerSpecialtyService.list();
        if (specialties.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 2. 获取所有已删除的讲师记录
        List<TrainLecturerDO> lecturers = trainLecturerService.lambdaQuery()
                .eq(TrainLecturerDO::getDeleted, false)
                .list();
        
        // 3. 计算每个专业领域的讲师数量
        Map<Long, Long> specialtyCountMap = new HashMap<>();
        for (TrainLecturerSpecialtyDO specialty : specialties) {
            // 初始化每个专业的计数为0
            specialtyCountMap.put(specialty.getId(), 0L);
        }
        
        // 4. 遍历讲师，统计专业领域分布
        for (TrainLecturerDO lecturer : lecturers) {
            String specialtyIds = lecturer.getSpecialtyIds();
            if (specialtyIds != null && !specialtyIds.isEmpty()) {
                // 专业ID格式为 ",1,2,3," 需要去掉首尾的逗号并拆分
                String[] ids = specialtyIds.substring(1, specialtyIds.length() - 1).split(",");
                for (String idStr : ids) {
                    if (!idStr.isEmpty()) {
                        Long id = Long.parseLong(idStr);
                        if (specialtyCountMap.containsKey(id)) {
                            specialtyCountMap.put(id, specialtyCountMap.get(id) + 1);
                        }
                    }
                }
            }
        }
        
        // 5. 转换为响应VO
        return specialties.stream().map(specialty -> {
            LecturerDistributionRespVO vo = new LecturerDistributionRespVO();
            vo.setId(specialty.getId());
            vo.setName(specialty.getName());
            vo.setCount(specialtyCountMap.getOrDefault(specialty.getId(), 0L));
            return vo;
        }).collect(Collectors.toList());
    }
    
    @Override
    public List<LecturerDistributionRespVO> getLevelDistribution() {
        // 1. 获取所有讲师等级
        List<TrainLecturerLevelDO> levels = trainLecturerLevelService.list();
        if (levels.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 2. 遍历所有等级，统计每个等级的已删除讲师数量
        return levels.stream().map(level -> {
            LecturerDistributionRespVO vo = new LecturerDistributionRespVO();
            vo.setId(level.getId());
            vo.setName(level.getName());
            // 查询该等级的已删除讲师数量
            long count = trainLecturerService.lambdaQuery()
                    .eq(TrainLecturerDO::getLecturerLevelId, level.getId())
                    .eq(TrainLecturerDO::getDeleted, false)
                    .count();
            vo.setCount(count);
            return vo;
        }).collect(Collectors.toList());
    }
    
    @Override
    public CourseSummaryRespVO getCourseSummary(CourseSummaryReqVO reqVO) {
        CourseSummaryRespVO respVO = new CourseSummaryRespVO();
        
        // 创建查询条件
        LambdaQueryWrapper<CourseDO> onlineQuery = createBaseQueryWrapper(reqVO)
                .eq(CourseDO::getCourseType, CourseTypeEnum.ONLINE.getCode()); // 线上课程
        
        LambdaQueryWrapper<CourseDO> offlineQuery = createBaseQueryWrapper(reqVO)
                .eq(CourseDO::getCourseType, CourseTypeEnum.OFFLINE.getCode()); // 线下课程
        
        // 查询已发布的线上课程数量
        long onlineCourseCount = courseService.count(onlineQuery);
        respVO.setOnlineCourseCount(onlineCourseCount);
        
        // 查询已发布的线下课程数量
        long offlineCourseCount = courseService.count(offlineQuery);
        respVO.setOfflineCourseCount(offlineCourseCount);
        
        // 查询线上课程的总课时（直接累加course_hours）
        List<CourseDO> onlineCourses = courseService.list(
                onlineQuery.select(CourseDO::getCourseHours));
        long onlineCourseDuration = onlineCourses.stream()
                .filter(Objects::nonNull)
                .mapToLong(course -> {
                    Integer courseHours = course.getCourseHours();
                    return courseHours != null && courseHours > 0 ? courseHours : 0L;
                })
                .sum();
        respVO.setOnlineCourseDuration(onlineCourseDuration);
        
        // 查询线下课程总课时（直接累加course_hours）
        List<CourseDO> offlineCourses = courseService.list(
                offlineQuery.select(CourseDO::getCourseHours));
        long offlineCourseDuration = offlineCourses.stream()
                .filter(Objects::nonNull)
                .mapToLong(course -> {
                    Integer courseHours = course.getCourseHours();
                    return courseHours != null && courseHours > 0 ? courseHours : 0L;
                })
                .sum();
        respVO.setOfflineCourseDuration(offlineCourseDuration);
        
        return respVO;
    }
    
    @Override
    public List<LecturerDetailRespVO> getLecturerDetailList(LecturerDetailReqVO reqVO) {
        // 1. 构建查询条件 (针对 TrainLecturerDO)
        LambdaQueryWrapper<TrainLecturerDO> lecturerQueryWrapper = new LambdaQueryWrapper<TrainLecturerDO>();

        // 讲师姓名模糊查询需要关联用户表，根据用户昵称进行筛选
        List<Long> userIds = Collections.emptyList();
        if (ObjectUtil.isNotEmpty(reqVO.getName())) {
            // AdminUserApi没有提供getUserListByNickname方法，采用临时方案
            // 先获取所有用户再过滤匹配的用户
            CommonResult<List<AdminUserRespDTO>> userResult = adminUserApi.getAllUser();
            if (userResult.getData() != null && !userResult.getData().isEmpty()) {
                userIds = userResult.getData().stream()
                        .filter(user -> user.getNickname() != null && user.getNickname().contains(reqVO.getName()))
                        .map(AdminUserRespDTO::getId)
                        .collect(Collectors.toList());
                
                // 如果没有找到匹配的用户，直接返回空结果
                if (userIds.isEmpty()) {
                    return Collections.emptyList();
                }
                
                lecturerQueryWrapper.in(TrainLecturerDO::getUserId, userIds);
            } else {
                // 如果没有找到匹配的用户，直接返回空结果
                return Collections.emptyList();
            }
        }

        // 部门筛选：假设部门ID是精确匹配 dept_scope_ids 字段中的一个，格式为,ID,ID,
        if (reqVO.getDepartmentId() != null) {
            lecturerQueryWrapper.like(TrainLecturerDO::getDeptScopeIds, "," + reqVO.getDepartmentId() + ",");
        }

        // 岗位筛选：岗位ID需要通过用户表关联，根据用户岗位ID筛选讲师ID
        if (reqVO.getPositionId() != null) {
            // 获取指定岗位的用户列表
            CommonResult<List<AdminUserRespDTO>> userResult = adminUserApi.getUserListByPostIds(Collections.singleton(reqVO.getPositionId()));
            if (userResult.getData() != null && !userResult.getData().isEmpty()) {
                List<Long> positionUserIds = userResult.getData().stream()
                        .map(AdminUserRespDTO::getId)
                        .collect(Collectors.toList());
                // 如果已经有姓名筛选的结果，则取交集
                if (!userIds.isEmpty()) {
                    positionUserIds.retainAll(userIds);
                    if (positionUserIds.isEmpty()) {
                        return Collections.emptyList();
                    }
                }
                lecturerQueryWrapper.in(TrainLecturerDO::getUserId, positionUserIds);
            } else {
                // 如果没有匹配的用户，直接返回空结果
                return Collections.emptyList();
            }
        }

        // 2. 查询已删除的讲师列表
        List<TrainLecturerDO> lecturers = trainLecturerService.lambdaQuery()
                .eq(TrainLecturerDO::getDeleted, false)
                .list();

        if (lecturers.isEmpty()) {
            return new ArrayList<>();
        }

        // 3. 批量获取讲师关联信息（部门、岗位、用户昵称、等级、领域）以提高效率
        // 3.1 收集所有讲师的用户ID、讲师等级ID、专业领域ID
        Set<Long> lecturerUserIds = lecturers.stream()
                .map(lecturer -> {
                    try {
                        return Long.parseLong(lecturer.getUserId());
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Set<Long> lecturerLevelIds = lecturers.stream()
                .map(TrainLecturerDO::getLecturerLevelId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 3.2 批量查询用户信息
        Map<Long, AdminUserRespDTO> userMap = new HashMap<>();
        if (!lecturerUserIds.isEmpty()) {
            CommonResult<List<AdminUserRespDTO>> userResult = adminUserApi.getUserList(lecturerUserIds);
            if (userResult.getData() != null) {
                userMap = userResult.getData().stream()
                        .collect(Collectors.toMap(AdminUserRespDTO::getId, user -> user));
            }
        }
        
        // 3.3 批量查询部门信息
        Set<Long> deptIds = new HashSet<>();
        for (AdminUserRespDTO user : userMap.values()) {
            if (user.getDeptId() != null) {
                deptIds.add(user.getDeptId());
            }
        }
        
        Map<Long, DeptRespDTO> deptMap = new HashMap<>();
        if (!deptIds.isEmpty()) {
            CommonResult<List<DeptRespDTO>> deptResult = deptApi.getDeptList(deptIds);
            if (deptResult.getData() != null) {
                deptMap = deptResult.getData().stream()
                        .collect(Collectors.toMap(DeptRespDTO::getId, dept -> dept));
            }
        }
        
        // 3.4 批量查询岗位信息
        Set<Long> postIds = new HashSet<>();
        for (AdminUserRespDTO user : userMap.values()) {
            if (user.getPostIds() != null) {
                postIds.addAll(user.getPostIds());
            }
        }
        
        Map<Long, PostRespDTO> postMap = new HashMap<>();
        if (!postIds.isEmpty()) {
            CommonResult<List<PostRespDTO>> postResult = postApi.getPostList(postIds);
            if (postResult.getData() != null) {
                postMap = postResult.getData().stream()
                        .collect(Collectors.toMap(PostRespDTO::getId, post -> post));
            }
        }
        
        // 3.5 批量查询讲师等级信息
        Map<Long, TrainLecturerLevelDO> levelMap = new HashMap<>();
        if (!lecturerLevelIds.isEmpty()) {
            List<TrainLecturerLevelDO> levels = trainLecturerLevelService.listByIds(lecturerLevelIds);
            levelMap = levels.stream()
                    .collect(Collectors.toMap(TrainLecturerLevelDO::getId, level -> level));
        }
        
        // 3.6 获取所有专业领域信息用于格式化领域名称
        List<TrainLecturerSpecialtyDO> allSpecialties = trainLecturerSpecialtyService.list();
        Map<Long, TrainLecturerSpecialtyDO> specialtyMap = allSpecialties.stream()
                .collect(Collectors.toMap(TrainLecturerSpecialtyDO::getId, specialty -> specialty));

        // 4. 准备查询课程统计信息所需的条件
        // 创建查询线上线下课程的条件
        LambdaQueryWrapper<CourseDO> onlineCourseQuery = new LambdaQueryWrapper<CourseDO>()
                .eq(CourseDO::getCourseType, CourseTypeEnum.ONLINE.getCode())
                .eq(CourseDO::getStatus, 1) // 已发布
                .eq(CourseDO::getDeleted, false); // 只返回未删除的数据
                
        LambdaQueryWrapper<CourseDO> offlineCourseQuery = new LambdaQueryWrapper<CourseDO>()
                .eq(CourseDO::getCourseType, CourseTypeEnum.OFFLINE.getCode())
                .eq(CourseDO::getStatus, 1) // 已发布
                .eq(CourseDO::getDeleted, false); // 只返回未删除的数据
                
        // 添加时间范围条件，如果有的话
        if (reqVO.getBeginTime() != null) {
            onlineCourseQuery.ge(CourseDO::getPublishTime, reqVO.getBeginTime());
            offlineCourseQuery.ge(CourseDO::getPublishTime, reqVO.getBeginTime());
        }
        
        if (reqVO.getEndTime() != null) {
            onlineCourseQuery.le(CourseDO::getPublishTime, reqVO.getEndTime());
            offlineCourseQuery.le(CourseDO::getPublishTime, reqVO.getEndTime());
        }

        // 5. 转换为响应VO并填充关联数据和统计数据
        List<LecturerDetailRespVO> respList = new ArrayList<>(lecturers.size());
        for (TrainLecturerDO lecturer : lecturers) {
            LecturerDetailRespVO resp = new LecturerDetailRespVO();

            // 设置讲师ID
            resp.setId(lecturer.getId());

            // 设置讲师姓名，从关联的用户信息中获取昵称
            Long userId = null;
            try {
                userId = Long.parseLong(lecturer.getUserId());
            } catch (NumberFormatException e) {
                // 用户ID解析错误，跳过
                continue;
            }
            
            AdminUserRespDTO user = userMap.get(userId);
            if (user != null) {
                resp.setName(user.getNickname());
                
                // 设置用户头像
                resp.setAvatar(user.getAvatar());
                
                // 填充所属部门名称
                DeptRespDTO dept = deptMap.get(user.getDeptId());
                if (dept != null) {
                    resp.setDepartmentName(dept.getName());
                }
                
                // 填充岗位名称
                if (CollUtil.isNotEmpty(user.getPostIds())) {
                    // 通常只取第一个岗位显示
                    Long firstPostId = user.getPostIds().iterator().next();
                    PostRespDTO post = postMap.get(firstPostId);
                    if (post != null) {
                        resp.setPositionName(post.getName());
                    }
                }
            }
            
            // 填充等级名称
            if (lecturer.getLecturerLevelId() != null) {
                TrainLecturerLevelDO level = levelMap.get(lecturer.getLecturerLevelId());
                if (level != null) {
                    resp.setLevel(level.getName());
                }
            }
            
            // 填充领域名称
            String specialtyIds = lecturer.getSpecialtyIds();
            if (StrUtil.isNotEmpty(specialtyIds)) {
                List<String> specialtyNames = new ArrayList<>();
                // 专业ID格式为 ",1,2,3," 需要去掉首尾的逗号并拆分
                String[] ids = specialtyIds.substring(1, specialtyIds.length() - 1).split(",");
                for (String idStr : ids) {
                    if (StrUtil.isNotEmpty(idStr)) {
                        try {
                            Long id = Long.parseLong(idStr);
                            TrainLecturerSpecialtyDO specialty = specialtyMap.get(id);
                            if (specialty != null) {
                                specialtyNames.add(specialty.getName());
                            }
                        } catch (NumberFormatException e) {
                            // 忽略解析错误
                        }
                    }
                }
                resp.setSpecialty(String.join("，", specialtyNames));
            }

            // 填充课程统计数据：查询该讲师作为教师的线上/线下课程
            try {
                // 讲师的用户ID
                Long lecturerUserId = Long.parseLong(lecturer.getUserId());
                
                // 线上课程统计
                LambdaQueryWrapper<CourseDO> lecturerOnlineQuery = onlineCourseQuery.clone();
                List<CourseDO> lecturerOnlineCourses = courseService.list(lecturerOnlineQuery);
                
                // 线上课程授课数
                resp.setOnlineCourseCount(lecturerOnlineCourses.size());
                
                // 线上课程总时长
                Double onlineCourseDuration = lecturerOnlineCourses.stream()
                        .filter(Objects::nonNull)
                        .mapToDouble(course -> {
                            Integer courseHours = course.getCourseHours();
                            return courseHours != null ? courseHours : 0;
                        })
                        .sum();
                resp.setOnlineCourseDuration(onlineCourseDuration);
                
                // 线上教学人次和评价统计 - 由于需要统计实际参与课程的人数和评价，这些可能需要关联其他表
                // 获取该讲师所有线上课程的ID列表
                List<Long> courseIds = lecturerOnlineCourses.stream()
                        .map(CourseDO::getId)
                        .collect(Collectors.toList());
                        
                // 获取这些课程的学生数量统计
                if (!courseIds.isEmpty()) {
                    // 获取所有课程的学生记录
                    List<CourseManagementDO> studentRecords = courseManagementService.getStudentCountByCourseIds(courseIds);
                    // 过滤出状态正常的学生记录，并计算总数
                    int studentCount = (int) studentRecords.stream()
                            .filter(record -> record.getUserType() == CourseUserTypeEnum.STUDENT.getCode()
                                    && record.getStatus() == 1) // 状态正常的学生
                            .count();
                    resp.setOnlineParticipants(studentCount);
                } else {
                    resp.setOnlineParticipants(0);
                }
                // TODO: 补充完整线上评价的计算
                resp.setOnlineEvaluation(0.0);
                
                // 线下课程统计
                LambdaQueryWrapper<CourseDO> lecturerOfflineQuery = offlineCourseQuery.clone();
                List<CourseDO> lecturerOfflineCourses = courseService.list(lecturerOfflineQuery);
                
                // 线下课程授课数
                resp.setOfflineCourseCount(lecturerOfflineCourses.size());
                
                // 线下授课学时(h)
                Double offlineTeachingHours = lecturerOfflineCourses.stream()
                        .filter(Objects::nonNull)
                        .mapToDouble(course -> {
                            Integer courseHours = course.getCourseHours();
                            return courseHours != null ? courseHours : 0;
                        })
                        .sum();
                resp.setOfflineTeachingHours(offlineTeachingHours);
                
                // 线下教学人次和评价统计 - 同样需要关联其他表
                // 获取该讲师所有线下课程的ID列表
                List<Long> offlineCourseIds = lecturerOfflineCourses.stream()
                        .map(CourseDO::getId)
                        .collect(Collectors.toList());
                        
                // 获取这些课程的学生数量统计
                if (!offlineCourseIds.isEmpty()) {
                    // 获取所有课程的学生记录
                    List<CourseManagementDO> offlineStudentRecords = courseManagementService.getStudentCountByCourseIds(offlineCourseIds);
                    // 过滤出状态正常的学生记录，并计算总数
                    int offlineStudentCount = (int) offlineStudentRecords.stream()
                            .filter(record -> record.getUserType() == CourseUserTypeEnum.STUDENT.getCode()
                                    && record.getStatus() == 1) // 状态正常的学生
                            .count();
                    resp.setOfflineParticipants(offlineStudentCount);
                } else {
                    resp.setOfflineParticipants(0);
                }
                // TODO: 补充完整线下评价的计算
                resp.setOfflineEvaluation(0.0);
                
            } catch (Exception e) {
                // 处理异常，设置默认值
                resp.setOnlineCourseCount(0);
                resp.setOnlineCourseDuration(0.0);
                resp.setOnlineParticipants(0);
                resp.setOnlineEvaluation(0.0);
                resp.setOfflineCourseCount(0);
                resp.setOfflineTeachingHours(0.0);
                resp.setOfflineParticipants(0);
                resp.setOfflineEvaluation(0.0);
                // 记录异常信息，便于排查
                // 如果有日志工具，可以在这里记录异常信息：logger.error("统计讲师数据异常", e);
            }

            // 教学奖励积分计算 - 需要关联积分表或其他相关表
            // TODO: 补充完整期内教学奖励积分的计算
            resp.setPeriodRewardPoints(0);
            
            // TODO: 补充完整累计教学奖励积分的计算
            resp.setTotalRewardPoints(0);

            respList.add(resp);
        }

        return respList;
    }
    
    /**
     * 创建基础查询条件包装器
     *
     * @param reqVO 请求参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<CourseDO> createBaseQueryWrapper(CourseSummaryReqVO reqVO) {
        LambdaQueryWrapper<CourseDO> queryWrapper = new LambdaQueryWrapper<CourseDO>()
                .eq(CourseDO::getStatus, 1) // 已发布
                .eq(CourseDO::getDeleted, false); // 只返回未删除的数据
        
        // 当reqVO为null时，返回未添加筛选条件的查询器，查询所有数据
        if (reqVO == null) {
            return queryWrapper;
        }
        
        // 添加部门筛选条件
        if (reqVO.getDepartmentId() != null) {
            queryWrapper.eq(CourseDO::getDepartmentId, reqVO.getDepartmentId());
        }
        
        // 添加时间范围条件
        if (reqVO.getBeginTime() != null) {
            queryWrapper.ge(CourseDO::getPublishTime, reqVO.getBeginTime());
        }
        
        if (reqVO.getEndTime() != null) {
            queryWrapper.le(CourseDO::getPublishTime, reqVO.getEndTime());
        }
        
        return queryWrapper;
    }

    @Override
    public List<CourseStatisticsRespVO> getCourseStatisticsList(CourseStatisticsReqVO reqVO) {
        // 1. 构建查询条件
        LambdaQueryWrapper<CourseDO> queryWrapper = new LambdaQueryWrapper<CourseDO>()
                .eq(CourseDO::getStatus, 1) // 已发布状态
                .eq(CourseDO::getDeleted, false); // 只返回未删除的数据
        
        // 添加条件：课程类型
        if (reqVO.getCourseType() != null) {
            queryWrapper.eq(CourseDO::getCourseType, reqVO.getCourseType());
        }
        
        // 添加条件：时间范围
        if (reqVO.getBeginTime() != null) {
            queryWrapper.ge(CourseDO::getPublishTime, reqVO.getBeginTime());
        }
        
        if (reqVO.getEndTime() != null) {
            queryWrapper.le(CourseDO::getPublishTime, reqVO.getEndTime());
        }
        
        // 2. 查询课程列表
        List<CourseDO> courses = courseService.list(queryWrapper);
        if (CollUtil.isEmpty(courses)) {
            return Collections.emptyList();
        }
        
        // 3. 获取所有课程ID，用于批量查询相关数据
        List<Long> courseIds = courses.stream()
                .map(CourseDO::getId)
                .collect(Collectors.toList());

        // 4. 查询课程参与学习的用户
        final Map<Long, List<CourseManagementDO>> courseUserMap = courseManagementService.getStudentCountByCourseIds(courseIds).stream()
                .filter(record -> Objects.equals(record.getUserType(), CourseUserTypeEnum.STUDENT.getCode()))
                .collect(Collectors.groupingBy(CourseManagementDO::getCourseId));
        
        // 6. 转换为响应VO对象
        return courses.stream().map(course -> {
            CourseStatisticsRespVO respVO = new CourseStatisticsRespVO();
            
            // 设置基本信息
            respVO.setCourseName(course.getTitle());
            respVO.setCategoryName("全部"); // 默认分类名称
            respVO.setCreateTime(course.getPublishTime());
            
            // 获取课程的学习记录
            List<CourseManagementDO> userRecords = courseUserMap.getOrDefault(course.getId(), Collections.emptyList());
            
            // 计算学习人数
            int learningCount = userRecords.size();
            respVO.setLearningCount(learningCount);
            
            // 计算完成人数
            int completedCount = (int) userRecords.stream()
                    .filter(record -> record.getStatus() == 1) // 假设状态1表示已完成
                    .count();
            respVO.setCompletedCount(completedCount);
            
            // 设置课程总学习时长
            Integer totalDuration = course.getCourseHours() != null ? course.getCourseHours() : 0;
            respVO.setTotalLearningDuration(totalDuration);
            
            // 计算人均学习时长
            double avgDuration = learningCount > 0 ? (double) totalDuration / learningCount : 0;
            respVO.setAvgLearningDuration(avgDuration);
            
            // 设置课程评价 - 这里需要从评价表中查询，暂时设置默认值
            respVO.setCourseRating(4.5); // 默认值
            
            return respVO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<OnlineCourseDetailRespVO> getOnlineCourseDetailList(OnlineCourseDetailReqVO reqVO) {
        // 1. 构建查询条件
        LambdaQueryWrapper<CourseDO> queryWrapper = new LambdaQueryWrapper<CourseDO>()
                .eq(CourseDO::getStatus, 1) // 已发布状态
                .eq(CourseDO::getCourseType, CourseTypeEnum.ONLINE.getCode()) // 只查询线上课程
                .eq(CourseDO::getDeleted, false); // 只返回未删除的数据
        
        // 添加时间范围条件
        if (reqVO.getBeginTime() != null) {
            queryWrapper.ge(CourseDO::getPublishTime, reqVO.getBeginTime());
        }
        
        if (reqVO.getEndTime() != null) {
            queryWrapper.le(CourseDO::getPublishTime, reqVO.getEndTime());
        }
        
        // 添加模糊查询条件
        if (StrUtil.isNotBlank(reqVO.getKeyword())) {
            queryWrapper.and(q -> q.like(CourseDO::getTitle, reqVO.getKeyword())
                    .or()
                    .like(CourseDO::getSubtitle, reqVO.getKeyword()));
        }
        
        // 2. 查询课程列表
        List<CourseDO> courses = courseService.list(queryWrapper);
        if (CollUtil.isEmpty(courses)) {
            return PageResult.empty();
        }
        
        // 3. 获取所有课程ID，用于批量查询相关数据
        List<Long> courseIds = courses.stream()
                .map(CourseDO::getId)
                .collect(Collectors.toList());
        
        // 4. 查询课程的讲师信息
        List<CourseManagementDO> courseManagements = courseManagementService.getStudentCountByCourseIds(courseIds);

        // 筛选讲师记录
        Stream<CourseManagementDO> teacherStream = courseManagements.stream()
                .filter(record -> Objects.equals(record.getUserType(), CourseUserTypeEnum.TEACHER.getCode()));

        // 如果指定了讲师ID，则需要先查找对应的用户ID
        Long targetUserId = null;
        if (reqVO.getLecturerId() != null) {
            // 根据讲师ID查找对应的用户ID
            TrainLecturerDO lecturer = trainLecturerService.getById(reqVO.getLecturerId());
            if (lecturer != null && lecturer.getUserId() != null) {
                try {
                    targetUserId = Long.parseLong(lecturer.getUserId());
                } catch (NumberFormatException e) {
                    // 用户ID解析错误，返回空结果
                    return new PageResult<>(Collections.emptyList(), 0L);
                }
            } else {
                // 讲师不存在，返回空结果
                return new PageResult<>(Collections.emptyList(), 0L);
            }
        }

        // 如果指定了讲师ID，则进一步筛选
        if (targetUserId != null) {
            final Long finalTargetUserId = targetUserId;
            teacherStream = teacherStream.filter(record -> Objects.equals(record.getUserId(), finalTargetUserId));
        }

        Long total = teacherStream.count();

        // 重新创建流进行分页
        teacherStream = courseManagements.stream()
                .filter(record -> Objects.equals(record.getUserType(), CourseUserTypeEnum.TEACHER.getCode()));

        // 如果指定了讲师ID，则进一步筛选
        if (targetUserId != null) {
            final Long finalTargetUserId = targetUserId;
            teacherStream = teacherStream.filter(record -> Objects.equals(record.getUserId(), finalTargetUserId));
        }

        List<CourseManagementDO> teacherRecords = teacherStream
                .skip((long) (reqVO.getPageNo() - 1) * reqVO.getPageSize())
                .limit(reqVO.getPageSize())
                .collect(Collectors.toList());
        
        // 按课程ID分组讲师记录
        final Map<Long, List<CourseManagementDO>> courseTeacherMap = teacherRecords.stream()
                .collect(Collectors.groupingBy(CourseManagementDO::getCourseId));
        
        // 5. 获取讲师ID列表，用于查询讲师详细信息
        Set<Long> teacherIds = teacherRecords.stream()
                .map(CourseManagementDO::getUserId)
                .collect(Collectors.toSet());
        
        // 6. 查询讲师用户信息
        final Map<Long, AdminUserRespDTO> userMap;
        if (!teacherIds.isEmpty()) {
            CommonResult<List<AdminUserRespDTO>> userResult = adminUserApi.getUserList(teacherIds);
            if (userResult.getData() != null) {
                userMap = userResult.getData().stream()
                        .collect(Collectors.toMap(AdminUserRespDTO::getId, user -> user));
            } else {
                userMap = new HashMap<>();
            }
        } else {
            userMap = new HashMap<>();
        }
        
        // 7. 查询部门信息
        Set<Long> deptIds = new HashSet<>();
        for (AdminUserRespDTO user : userMap.values()) {
            if (user.getDeptId() != null) {
                deptIds.add(user.getDeptId());
            }
        }
        
        Map<Long, DeptRespDTO> deptMap = new HashMap<>();
        if (!deptIds.isEmpty()) {
            CommonResult<List<DeptRespDTO>> deptResult = deptApi.getDeptList(deptIds);
            if (deptResult.getData() != null) {
                deptMap = deptResult.getData().stream()
                        .collect(Collectors.toMap(DeptRespDTO::getId, dept -> dept));
            }
        }
        
        // 8. 如果有部门ID过滤条件，筛选符合条件的课程
        if (reqVO.getDepartmentId() != null) {
            courses = courses.stream()
                    .filter(course -> {
                        List<CourseManagementDO> teachers = courseTeacherMap.getOrDefault(course.getId(), Collections.emptyList());
                        for (CourseManagementDO teacher : teachers) {
                            AdminUserRespDTO user = userMap.get(teacher.getUserId());
                            if (user != null && reqVO.getDepartmentId().equals(user.getDeptId())) {
                                return true;
                            }
                        }
                        return false;
                    })
                    .collect(Collectors.toList());

            if (courses.isEmpty()) {
                return PageResult.empty();
            }
        }

        // 9. 如果有讲师姓名过滤条件，筛选符合条件的课程
        if (StrUtil.isNotBlank(reqVO.getLecturerName())) {
            courses = courses.stream()
                    .filter(course -> {
                        List<CourseManagementDO> teachers = courseTeacherMap.getOrDefault(course.getId(), Collections.emptyList());
                        for (CourseManagementDO teacher : teachers) {
                            AdminUserRespDTO user = userMap.get(teacher.getUserId());
                            if (user != null && user.getNickname() != null &&
                                user.getNickname().contains(reqVO.getLecturerName())) {
                                return true;
                            }
                        }
                        return false;
                    })
                    .collect(Collectors.toList());

            if (courses.isEmpty()) {
                return PageResult.empty();
            }
        }

        // 10. 如果有课程名称过滤条件，筛选符合条件的课程
        if (StrUtil.isNotBlank(reqVO.getCourseName())) {
            courses = courses.stream()
                    .filter(course -> course.getTitle() != null && course.getTitle().contains(reqVO.getCourseName()))
                    .collect(Collectors.toList());

            if (courses.isEmpty()) {
                return PageResult.empty();
            }
        }
        
        // 11. 查询学生记录，用于统计加入人数和完成人数
        List<CourseManagementDO> studentRecords = courseManagements.stream()
                .filter(record -> record.getUserType() == CourseUserTypeEnum.STUDENT.getCode())
                .collect(Collectors.toList());

        Map<Long, List<CourseManagementDO>> courseStudentMap = studentRecords.stream()
                .collect(Collectors.groupingBy(CourseManagementDO::getCourseId));

        // 12. 转换为响应VO对象
        List<OnlineCourseDetailRespVO> respList = new ArrayList<>();
        for (CourseDO course : courses) {
            // 获取课程的讲师
            List<CourseManagementDO> teachers = courseTeacherMap.getOrDefault(course.getId(), Collections.emptyList());
            if (teachers.isEmpty()) {
                // 如果没有讲师，创建一个默认的课程记录
                OnlineCourseDetailRespVO respVO = createOnlineCourseDetailResp(course, null, null, null, courseStudentMap);
                respList.add(respVO);
                continue;
            }
            
            // 为每个讲师创建一条记录
            for (CourseManagementDO teacher : teachers) {
                AdminUserRespDTO user = userMap.get(teacher.getUserId());
                if (user == null) {
                    continue;
                }
                
                DeptRespDTO dept = null;
                if (user.getDeptId() != null) {
                    dept = deptMap.get(user.getDeptId());
                }
                
                OnlineCourseDetailRespVO respVO = createOnlineCourseDetailResp(course, teacher, user, dept, courseStudentMap);
                respList.add(respVO);
            }
        }
        
        return new PageResult<>(respList, total);
    }

    /**
     * 创建线上课程明细响应对象
     */
    private OnlineCourseDetailRespVO createOnlineCourseDetailResp(CourseDO course, CourseManagementDO teacher, 
                                                                AdminUserRespDTO user, DeptRespDTO dept,
                                                                Map<Long, List<CourseManagementDO>> courseStudentMap) {
        OnlineCourseDetailRespVO respVO = new OnlineCourseDetailRespVO();
        
        // 设置课程信息
        respVO.setCourseName(course.getTitle());
        respVO.setCourseCode(course.getId().toString()); // 使用课程ID作为编号
        respVO.setCreateTime(course.getPublishTime());
        respVO.setCourseCategory(getCategoryName(course.getCategoryId()));
        
        // 设置讲师信息
        if (teacher != null && user != null) {
            respVO.setLecturerName(user.getNickname()); // 讲师姓名使用昵称
//            respVO.setLecturerUsername(user.getUsername()); // 讲师用户名使用账号
            TrainLecturerDO lecturerDO = trainLecturerService.getById(teacher.getUserId());
            if(lecturerDO != null){
                TrainLecturerLevelDO lecturerLevelDO = trainLecturerLevelService.getById(user.getId());
                if(lecturerLevelDO != null){
                    respVO.setLecturerLevel(lecturerLevelDO.getName());
                }
            }
            if(respVO.getLecturerLevel() == null){
                respVO.setLecturerLevel("普通讲师"); // 默认讲师等级
            }
            if (dept != null) {
                respVO.setLecturerDepartment(dept.getName());
            } else {
                respVO.setLecturerDepartment("");
            }
        } else {
            respVO.setLecturerName("");
            respVO.setLecturerUsername("");
            respVO.setLecturerLevel("");
            respVO.setLecturerDepartment("");
        }
        
        // 统计学生数据
        List<CourseManagementDO> students = courseStudentMap.getOrDefault(course.getId(), Collections.emptyList());
        int joinCount = students.size();
        int completeCount = (int) students.stream()
                .filter(s -> s.getStatus() == 1) // 假设状态1表示已完成
                .count();
        
        respVO.setJoinCount(joinCount);
        respVO.setCompleteCount(completeCount);
        
        // 设置学习时长
        Double totalDuration = course.getCourseHours() != null ? course.getCourseHours().doubleValue() : 0.0;
        respVO.setLearningDuration(totalDuration);
        
        // 计算人均学习时长
        double avgDuration = joinCount > 0 ? totalDuration / joinCount : 0.0;
        respVO.setAvgLearningDuration(avgDuration);
        
        // 设置课程评价 - 默认值
        respVO.setCourseRating(4.5);
        
        return respVO;
    }

    /**
     * 获取课程分类名称
     */
    private String getCategoryName(Long categoryId) {
        if (categoryId == null) {
            return "未分类";
        }
        CourseCategoryDO courseCategoryDO = courseCategoryMapper.selectById(categoryId);
        if(courseCategoryDO != null){
            return courseCategoryDO.getName();
        }else {
            return "未分类";
        }
    }

    @Override
    public List<OfflineCourseDetailRespVO> getOfflineCourseDetailList(OfflineCourseDetailReqVO reqVO) {
        // 1. 构建查询条件 - 查询线下课程
        LambdaQueryWrapper<CourseDO> queryWrapper = new LambdaQueryWrapper<CourseDO>()
                .eq(CourseDO::getStatus, 1) // 已发布状态
                .eq(CourseDO::getCourseType, CourseTypeEnum.OFFLINE.getCode()) // 只查询线下课程
                .eq(CourseDO::getDeleted, false); // 只返回未删除的数据

        // 添加时间范围条件
        if (reqVO.getBeginTime() != null) {
            queryWrapper.ge(CourseDO::getPublishTime, reqVO.getBeginTime());
        }

        if (reqVO.getEndTime() != null) {
            queryWrapper.le(CourseDO::getPublishTime, reqVO.getEndTime());
        }

        // 添加模糊查询条件
        if (StrUtil.isNotBlank(reqVO.getKeyword())) {
            queryWrapper.and(q -> q.like(CourseDO::getTitle, reqVO.getKeyword())
                    .or()
                    .like(CourseDO::getSubtitle, reqVO.getKeyword()));
        }

        // 2. 查询课程列表
        List<CourseDO> courses = courseService.list(queryWrapper);
        if (CollUtil.isEmpty(courses)) {
            return Collections.emptyList();
        }

        // 3. 获取所有课程ID，用于查询班次信息
        List<Long> courseIds = courses.stream()
                .map(CourseDO::getId)
                .collect(Collectors.toList());

        // 4. 查询这些课程对应的班次信息（获取讲师信息）
        LambdaQueryWrapper<ClassEntity> classWrapper = new LambdaQueryWrapper<>();
        classWrapper.in(ClassEntity::getCourseId, courseIds);

        // 如果指定了讲师ID，则筛选该讲师的班次
        if (reqVO.getLecturerId() != null) {
            // 根据讲师ID查找对应的用户ID
            TrainLecturerDO lecturer = trainLecturerService.getById(reqVO.getLecturerId());
            if (lecturer != null && lecturer.getUserId() != null) {
                try {
                    Long instructorUserId = Long.parseLong(lecturer.getUserId());
                    classWrapper.eq(ClassEntity::getInstructorId, instructorUserId);
                } catch (NumberFormatException e) {
                    // 用户ID解析错误，返回空结果
                    return Collections.emptyList();
                }
            } else {
                // 讲师不存在，返回空结果
                return Collections.emptyList();
            }
        }

        List<ClassEntity> classList = classService.list(classWrapper);

        // 5. 按课程ID分组班次，获取每个课程的讲师信息
        Map<Long, List<ClassEntity>> courseClassMap = classList.stream()
                .collect(Collectors.groupingBy(ClassEntity::getCourseId));

        // 6. 获取所有讲师ID列表
        Set<Long> instructorIds = classList.stream()
                .map(ClassEntity::getInstructorId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 7. 批量查询讲师用户信息
        final Map<Long, AdminUserRespDTO> userMap;
        if (!instructorIds.isEmpty()) {
            CommonResult<List<AdminUserRespDTO>> userResult = adminUserApi.getUserList(instructorIds);
            if (userResult.getData() != null) {
                userMap = userResult.getData().stream()
                        .collect(Collectors.toMap(AdminUserRespDTO::getId, user -> user));
            } else {
                userMap = new HashMap<>();
            }
        } else {
            userMap = new HashMap<>();
        }

        // 8. 批量查询讲师详细信息
        final Map<Long, TrainLecturerDO> lecturerMap;
        if (!instructorIds.isEmpty()) {
            // 根据用户ID查询讲师信息
            LambdaQueryWrapper<TrainLecturerDO> lecturerWrapper = new LambdaQueryWrapper<>();
            lecturerWrapper.in(TrainLecturerDO::getUserId,
                    instructorIds.stream().map(String::valueOf).collect(Collectors.toList()));
            List<TrainLecturerDO> lecturers = trainLecturerService.list(lecturerWrapper);
            lecturerMap = lecturers.stream()
                    .collect(Collectors.toMap(
                            lecturer -> Long.parseLong(lecturer.getUserId()),
                            lecturer -> lecturer,
                            (existing, replacement) -> existing
                    ));
        } else {
            lecturerMap = new HashMap<>();
        }

        // 9. 批量查询部门信息
        Set<Long> deptIds = userMap.values().stream()
                .map(AdminUserRespDTO::getDeptId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        final Map<Long, DeptRespDTO> deptMap;
        if (!deptIds.isEmpty()) {
            CommonResult<List<DeptRespDTO>> deptResult = deptApi.getDeptList(deptIds);
            if (deptResult.getData() != null) {
                deptMap = deptResult.getData().stream()
                        .collect(Collectors.toMap(DeptRespDTO::getId, dept -> dept));
            } else {
                deptMap = new HashMap<>();
            }
        } else {
            deptMap = new HashMap<>();
        }

        // 10. 构建结果列表（以课程为主体，从班次中获取讲师信息）
        List<OfflineCourseDetailRespVO> result = new ArrayList<>();

        for (CourseDO course : courses) {
            // 获取该课程对应的班次列表
            List<ClassEntity> courseClasses = courseClassMap.getOrDefault(course.getId(), Collections.emptyList());

            if (courseClasses.isEmpty()) {
                // 如果课程没有开班，创建一个没有讲师信息的记录
                OfflineCourseDetailRespVO respVO = createOfflineCourseDetailResp(course, null, null, null, null);
                result.add(respVO);
            } else {
                // 为每个班次创建一条记录（一个课程可能有多个班次，每个班次可能有不同的讲师）
                for (ClassEntity classEntity : courseClasses) {
                    AdminUserRespDTO user = userMap.get(classEntity.getInstructorId());
                    TrainLecturerDO teacher = lecturerMap.get(classEntity.getInstructorId());
                    DeptRespDTO dept = user != null ? deptMap.get(user.getDeptId()) : null;

                    OfflineCourseDetailRespVO respVO = createOfflineCourseDetailResp(course, teacher, user, dept, classEntity);
                    result.add(respVO);
                }
            }
        }

        return result;
    }

    /**
     * 创建线下课程明细响应对象（以课程为主体，从班次获取讲师信息）
     */
    private OfflineCourseDetailRespVO createOfflineCourseDetailResp(CourseDO course, TrainLecturerDO teacher,
                                                                  AdminUserRespDTO user, DeptRespDTO dept,
                                                                  ClassEntity classEntity) {
        OfflineCourseDetailRespVO respVO = new OfflineCourseDetailRespVO();

        // 设置课程信息（主体）
        if (course != null) {
            respVO.setCourseName(course.getTitle());
            respVO.setCourseCode(course.getId().toString()); // 使用课程ID作为编号
            respVO.setCreateTime(course.getPublishTime());
            respVO.setCourseCategory(getCategoryName(course.getCategoryId()));
        } else {
            respVO.setCourseName("未知课程");
            respVO.setCourseCode("");
            respVO.setCreateTime(null);
            respVO.setCourseCategory("");
        }

        // 设置班次信息（如果有开班）
        if (classEntity != null) {
            respVO.setClassName(classEntity.getClassName());
            respVO.setStartTime(classEntity.getStartTime());
            respVO.setEndTime(classEntity.getEndTime());
            respVO.setCourseLocation(classEntity.getLocation() != null ? classEntity.getLocation() : "未设置地点");

            // 设置人数统计（基于班次数据）
            respVO.setSignupCount(classEntity.getSignupNum() != null ? classEntity.getSignupNum() : 0);
            respVO.setJoinCount((classEntity.getSignupNum() != null ? classEntity.getSignupNum() : 0) +
                               (classEntity.getSupplementNum() != null ? classEntity.getSupplementNum() : 0));
        } else {
            // 课程没有开班的情况
            respVO.setClassName("未开班");
            respVO.setStartTime(null);
            respVO.setEndTime(null);
            respVO.setCourseLocation("未设置地点");
            respVO.setSignupCount(0);
            respVO.setJoinCount(0);
        }

        // 设置讲师信息
        if (teacher != null && user != null) {
            respVO.setLecturerName(user.getNickname()); // 讲师姓名使用昵称
            respVO.setLecturerUsername(user.getUsername()); // 讲师用户名使用账号
            respVO.setLecturerLevel("普通讲师"); // 默认讲师等级，实际应查询讲师等级

            if (dept != null) {
                respVO.setLecturerDepartment(dept.getName()); // 讲师所属部门
            } else {
                respVO.setLecturerDepartment("");
            }
        } else {
            respVO.setLecturerName("");
            respVO.setLecturerUsername("");
            respVO.setLecturerLevel("");
            respVO.setLecturerDepartment("");
        }

        // 设置课程时长（基于课程数据或班次时长）
        Double courseDuration = 0.0;
        if (course != null && course.getCourseHours() != null) {
            courseDuration = course.getCourseHours().doubleValue();
        } else if (classEntity != null && classEntity.getHours() != null) {
            courseDuration = classEntity.getHours().doubleValue();
        }
        respVO.setCourseDuration(courseDuration);

        // TODO: 设置其他统计数据，需要基于班次相关表进行统计
        respVO.setCompleteCount(0); // 需要从班次学员表统计完成人数
        respVO.setFullAttendanceCount(0); // 需要从签到表统计全勤人数
        respVO.setAbsenceCount(0); // 需要从签到表统计缺勤人数
        respVO.setLeaveCount(0); // 需要从签到表统计请假人数
        respVO.setHomeworkCompletionRate("0%"); // 需要从作业表统计完成率
        respVO.setOnlineExamPassRate("0%"); // 需要从考试表统计通过率
        respVO.setOfflineExamPassRate("0%"); // 需要从考试表统计通过率

        // 设置课程评价 - 需要从评价表查询真实数据
        respVO.setCourseRating(4.5); // 临时默认值，应该查询 train_user_rating 表

        return respVO;
    }
} 