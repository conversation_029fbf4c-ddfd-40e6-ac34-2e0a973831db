package cn.iocoder.yudao.module.train.controller.admin.data;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.train.model.data.req.*;
import cn.iocoder.yudao.module.train.model.data.resp.*;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.StaffComplexStatisticReq;
import cn.iocoder.yudao.module.train.controller.admin.data.vo.StaffComplexStatisticResp;
import cn.iocoder.yudao.module.train.service.data.IStaffCourseStatisticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/train/data/staff-course-export")
@Tag(name = "数据 - 员工数据统计导出接口")
public class StaffStatisticExportController {

    @Resource
    private IStaffCourseStatisticService staffCourseStatisticService;
    @GetMapping("/export")
    @Operation(summary = "导出员工线上课程统计数据")
    public void exportStaffOnlineCourse(@Valid StaffOnlineCourseReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffOnlineCourseResp> list = staffCourseStatisticService.exportStaffOnlineCourseList(exportReq);
        ExcelUtils.write(response, "员工线上课程统计数据.xlsx", "员工线上课程统计数据", StaffOnlineCourseResp.class, list);
    }

    @GetMapping("/detail/export")
    @Operation(summary = "导出员工线上课程明细明细数据")
    public void exportStaffOnlineCourseDetail(@Valid StaffOnlineCourseDetailReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffOnlineCourseDetailResp> list = staffCourseStatisticService.exportStaffOnlineCourseDetailList(exportReq);
        ExcelUtils.write(response, "员工线上课程明细明细数据.xlsx", "员工线上课程明细明细数据", StaffOnlineCourseDetailResp.class, list);
    }

    @GetMapping("/project/export")
    @Operation(summary = "导出员工培训项目统计数据")
    public void exportStaffProjectStatistic(@Valid StaffProjectStatisticReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffProjectStatisticResp> list = staffCourseStatisticService.exportStaffProjectStatisticList(exportReq);
        ExcelUtils.write(response, "员工培训项目统计数据.xlsx", "员工培训项目统计数据", StaffProjectStatisticResp.class, list);
    }

    @GetMapping("/project/detail/export")
    @Operation(summary = "导出员工培训项目明细统计数据")
    public void exportStaffProjectDetail(@Valid StaffProjectDetailReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffProjectDetailResp> list = staffCourseStatisticService.exportStaffProjectDetailList(exportReq);
        ExcelUtils.write(response, "员工培训项目统计数据.xlsx", "员工培训项目统计数据", StaffProjectDetailResp.class, list);
    }


    
    @GetMapping("/offline/export")
    @Operation(summary = "导出员工线下课程明细数据")
    public void exportStaffOfflineCourse(@Valid StaffOfflineCourseReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffOfflineCourseResp> list = staffCourseStatisticService.exportStaffOfflineCourseList(exportReq);
        ExcelUtils.write(response, "员工线下课程明细数据.xlsx", "员工线下课程明细数据", StaffOfflineCourseResp.class, list);
    }
    
    @GetMapping("/offline/class/export")
    @Operation(summary = "导出员工线下课程开班明细数据")
    public void exportStaffOfflineClass(@Valid StaffOfflineClassReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffOfflineClassResp> list = staffCourseStatisticService.exportStaffOfflineClassList(exportReq);
        ExcelUtils.write(response, "员工线下课程开班明细数据.xlsx", "员工线下课程开班明细数据", StaffOfflineClassResp.class, list);
    }

    @GetMapping("/staff/complex-statistics/export")
    @Operation(summary = "导出员工数据明细")
    public void exportStaffComplexStatistics(@Valid StaffComplexStatisticReq exportReq, HttpServletResponse response) throws IOException {
        List<StaffComplexStatisticResp> list = staffCourseStatisticService.exportStaffComplexStatisticsList(exportReq);
        ExcelUtils.write(response, "员工数据明细.xlsx", "员工数据明细", StaffComplexStatisticResp.class, list);
    }
}
