<template>
  <el-card>
    <div class="integral-data">
      <el-tabs v-model="activeTab" type="card">
        <!-- 概览 tab -->
        <el-tab-pane label="概览" name="overview">
          <div class="overview-container">
            <div class="section-title">积分数据概览</div>

            <!-- 搜索表单 -->
            <div class="white-block">
              <div class="search-form">
                <el-form :inline="true">
                  <el-form-item label="选择统计周期">
                    <el-date-picker
                      v-model="dateRange"
                      type="datetimerange"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM-DDTHH:mm:ss"
                      format="YYYY-MM-DD HH:mm:ss"
                      style="width: 360px"
                      @change="handleDateChange"
                    />
                  </el-form-item>
                  <el-form-item label="所属部门">
                    <DeptTreeSelect v-model="queryParams.departmentName" style="width: 200px" />
                  </el-form-item>
                  <el-form-item label="岗位选择">
                    <PostTreeSelect v-model="queryParams.position" style="width: 200px" />
                  </el-form-item>
                  <!-- <el-form-item label="角色">
                    <RoleTreeSelect v-model="queryParams.rank" style="width: 200px" />
                  </el-form-item> -->
                  <!-- <el-form-item label="用户组">
                    <el-select v-model="queryParams.userGroup" placeholder="全部">
                      <el-option label="全部" value="" />
                    </el-select>
                  </el-form-item> -->
                  <el-form-item label="入职时间">
                    <el-select v-model="queryParams.joinTime" style="width: 120px">
                      <el-option label="大于等于" value="gte" />
                    </el-select>
                    <el-input
                      v-model="queryParams.joinDays"
                      placeholder="请输入"
                      style="width: 80px; margin: 0 8px"
                    />
                    <span>天</span>
                  </el-form-item>
                  <el-form-item label="姓名">
                    <el-input
                      v-model="queryParams.keyword"
                      placeholder="请输入姓名"
                      clearable
                      style="width: 200px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" style="margin-left: 16px">
                      查询
                    </el-button>
                    <el-button @click="handleReset" style="margin-left: 16px">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 积分余额和积分消耗统计卡片 -->
            <div class="statistics-cards">
              <el-row :gutter="20">
                <!-- 积分余额卡片 - 占用较少空间 -->
                <el-col :span="8">
                  <div class="stat-card">
                    <div class="title">积分余额</div>
                    <div class="balance-content">
                      <div class="balance-row">
                        <div class="balance-item">
                          <div class="number">
                            {{ pointsBalanceData ? pointsBalanceData.totalPoints : 'N/A' }}
                          </div>
                          <div class="label">积分账户总额</div>
                        </div>
                        <div class="balance-item">
                          <div class="number">
                            {{
                              pointsBalanceData &&
                              typeof pointsBalanceData.averagePoints === 'number'
                                ? pointsBalanceData.averagePoints.toFixed(2)
                                : '0.00'
                            }}
                          </div>
                          <div class="label">人均余额</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <!-- 积分消耗卡片 - 占用较多空间 -->
                <el-col :span="16">
                  <div class="stat-card">
                    <div class="title">积分消耗</div>
                    <div class="consume-content">
                      <div class="consume-row">
                        <div class="consume-item">
                          <div class="number">{{ pointsConsumptionData.totalConsumption }}</div>
                          <div class="label">总消耗</div>
                        </div>
                        <div class="consume-item">
                          <div class="number">{{ pointsConsumptionData.exchangeProductCount }}</div>
                          <div class="label">兑换商品数</div>
                        </div>
                        <div class="consume-item">
                          <div class="number">{{ pointsConsumptionData.coverageCount }}</div>
                          <div class="label">覆盖人数</div>
                        </div>
                        <div class="consume-item">
                          <div class="number">{{ pointsConsumptionData.coverageRate }}%</div>
                          <div class="label">覆盖率</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 积分发放统计 -->
            <div class="release-statistics white-block">
              <div class="title">积分发放</div>
              <div class="stats-row">
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.totalDistribution }}</div>
                  <div class="label">总发放</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.learningReward }}</div>
                  <div class="label">学习奖励</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.interactionReward }}</div>
                  <div class="label">互动奖励</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.activityReward }}</div>
                  <div class="label">活跃奖励</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.teachingReward }}</div>
                  <div class="label">教学奖励</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.manualDistribution }}</div>
                  <div class="label">手动发放</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.distributionCount }}</div>
                  <div class="label">发放人次</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.coverageCount }}</div>
                  <div class="label">覆盖人数</div>
                </div>
                <div class="stat-item">
                  <div class="number">{{ pointsDistributionData.coverageRate }}%</div>
                  <div class="label">覆盖率</div>
                </div>
              </div>
            </div>

            <!-- 奖励次数图表 -->
            <div class="charts-container">
              <div class="chart-block white-block">
                <div class="title">奖励人次</div>
                <div ref="rewardCountChartRef" style="height: 400px"></div>
              </div>
              <div class="chart-block white-block">
                <div class="title">奖励分布</div>
                <div ref="rewardDistributionChartRef" style="height: 400px"></div>
              </div>
            </div>

            <!-- 十大热门兑换商品 -->
            <div class="hot-products white-block">
              <div class="title">十大热门兑换商品</div>
              <div ref="productsChartRef" style="height: 400px"></div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 明细 tab -->
        <el-tab-pane label="明细" name="detail">
          <div class="overview-container">
            <!-- 搜索表单 -->
            <div class="white-block">
              <div class="search-form">
                <el-form :inline="true">
                  <el-form-item label="选择统计周期">
                    <el-date-picker
                      v-model="detailDateRange"
                      type="datetimerange"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM-DDTHH:mm:ss"
                      format="YYYY-MM-DD HH:mm:ss"
                      style="width: 360px"
                      @change="handleDetailDateChange"
                    />
                  </el-form-item>
                  <el-form-item label="所属部门">
                    <DeptTreeSelect
                      v-model="detailQueryParams.departmentName"
                      style="width: 200px"
                    />
                  </el-form-item>
                  <el-form-item label="岗位选择">
                    <PostTreeSelect v-model="detailQueryParams.position" style="width: 200px" />
                  </el-form-item>
                  <!-- <el-form-item label="入职时间"> -->
                  <!-- <el-select v-model="detailQueryParams.joinTime" style="width: 120px">
                      <el-option label="大于等于" value="gte" />
                    </el-select>
                    <el-input
                      v-model="detailQueryParams.joinDays"
                      placeholder="请输入"
                      style="width: 80px; margin: 0 8px"
                    />
                    <span>天</span> -->
                  <el-form-item label="姓名">
                    <el-input
                      v-model="detailQueryParams.keyword"
                      placeholder="请输入姓名"
                      clearable
                      style="width: 200px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleDetailQuery" style="margin-left: 16px">
                      查询
                    </el-button>
                    <el-button @click="handleDetailReset" style="margin-left: 16px">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 表格 -->
            <div class="table-container white-block">
              <el-table :data="tableData" style="width: 100%" border>
                <el-table-column prop="name" label="姓名" />
                <el-table-column prop="username" label="用户名" />
                <el-table-column prop="departmentName" label="所属部门" />
                <el-table-column prop="postName" label="岗位" />
                <el-table-column prop="pointsBalance" label="积分余额" />
                <el-table-column prop="pointsGained" label="积分获得" />
                <el-table-column prop="pointsConsumed" label="积分消耗" />
                <el-table-column prop="learningReward" label="学习奖励" />
                <el-table-column prop="interactionReward" label="互动奖励" />
                <el-table-column prop="activityReward" label="活跃奖励" />
                <el-table-column prop="teachingReward" label="教学奖励" />
                <el-table-column prop="manualDistribution" label="手动发放" />
                <el-table-column label="操作" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="viewDetail(scope.row)">
                      查看明细
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import * as echarts from 'echarts'
import { IntegralDataApi } from '@/api/data/integralData/index'

const activeTab = ref('overview')

// 日期范围选择器
const dateRange = ref<[string, string] | undefined>(undefined)

// 明细页日期范围选择器
const detailDateRange = ref<[string, string] | undefined>(undefined)

// 日期范围变化处理
const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  } else {
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
}

// 明细页日期范围变化处理
const handleDetailDateChange = () => {
  if (detailDateRange.value && detailDateRange.value.length === 2) {
    detailQueryParams.value.startTime = detailDateRange.value[0]
    detailQueryParams.value.endTime = detailDateRange.value[1]
  } else {
    detailQueryParams.value.startTime = ''
    detailQueryParams.value.endTime = ''
  }
}

// 概览页数据
const queryParams = ref({
  startTime: '',
  endTime: '',
  departmentName: undefined as unknown as number,
  position: '',
  rank: '',
  userGroup: '',
  joinTime: 'gte',
  joinDays: '',
  keyword: ''
})

// 处理查询
const handleQuery = () => {
  // 根据接口参数要求调整参数
  const params = {
    deptId: queryParams.value.departmentName || undefined,
    postId: queryParams.value.position || undefined,
    roleId: queryParams.value.rank || undefined,
    employDaysGreaterThan: queryParams.value.joinDays || undefined,
    keyword: queryParams.value.keyword || undefined, // 添加姓名过滤
    pageNo: 1,
    pageSize: 10
  }

  // 移除空值参数
  Object.keys(params).forEach((key) => {
    if (params[key] === undefined || params[key] === null || params[key] === '') {
      delete params[key]
    }
  })

  console.log('概览页查询参数:', params)
  getDetailData()
}

// 处理重置
const handleReset = () => {
  dateRange.value = undefined
  queryParams.value = {
    startTime: '',
    endTime: '',
    departmentName: undefined as unknown as number,
    position: '',
    rank: '',
    userGroup: '',
    joinTime: 'gte',
    joinDays: '',
    keyword: ''
  }
  getDetailData()
}

// 积分发放统计数据
const releaseStats = ref([
  { label: '总发放', value: '140' },
  { label: '学习奖励', value: '51' },
  { label: '互动奖励', value: '9' },
  { label: '活跃奖励', value: '80' },
  { label: '教学奖励', value: '0' },
  { label: '手动发放', value: '0' },
  { label: '发放人次', value: '91' },
  { label: '覆盖人数', value: '11' },
  { label: '覆盖率', value: '18%' }
])

// 图表实例
let rewardCountChart: echarts.ECharts | null = null
let rewardDistributionChart: echarts.ECharts | null = null
let productsChart: echarts.ECharts | null = null

const rewardCountChartRef = ref<HTMLElement>()
const rewardDistributionChartRef = ref<HTMLElement>()
const productsChartRef = ref<HTMLElement>()

// 定义请求头
const headers = {
  'tenant-id': 1,
  Authorization: 'Bearer test1'
}

// 初始化表格数据
const tableData = ref([])

// 明细页查询参数
const detailQueryParams = ref({
  startTime: '',
  endTime: '',
  departmentName: undefined as unknown as number,
  position: undefined as unknown as number,
  joinTime: 'gte',
  joinDays: '',
  keyword: ''
})

// 过滤后的表格数据
const filteredTableData = computed(() => {
  return tableData.value.filter((item) => {
    // 时间过滤
    const startTime = detailQueryParams.value.startTime
    const endTime = detailQueryParams.value.endTime
    let timeMatch = true
    if (startTime && endTime) {
      timeMatch = item.updateTime >= startTime && item.updateTime <= endTime
    } else if (startTime) {
      timeMatch = item.updateTime >= startTime
    } else if (endTime) {
      timeMatch = item.updateTime <= endTime
    }

    // 部门过滤
    const deptMatch =
      !detailQueryParams.value.departmentName ||
      String(item.departmentName) === String(detailQueryParams.value.departmentName)

    // 岗位过滤
    const positionMatch =
      !detailQueryParams.value.position ||
      String(item.postName) === String(detailQueryParams.value.position)

    // 姓名过滤
    const nameMatch =
      !detailQueryParams.value.keyword || item.name.includes(detailQueryParams.value.keyword)

    return timeMatch && deptMatch && positionMatch && nameMatch
  })
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getDetailData()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDetailData()
}

// 获取明细数据
const getDetailData = async () => {
  try {
    // 根据接口参数要求调整参数
    const params = {
      deptId: detailQueryParams.value.departmentName || undefined,
      postId: detailQueryParams.value.position || undefined,
      roleId: undefined, // 暂时不传角色参数
      employDaysGreaterThan: detailQueryParams.value.joinDays || undefined,
      keyword: detailQueryParams.value.keyword || undefined, // 添加姓名过滤
      pageNo: currentPage.value,
      pageSize: pageSize.value
    }

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    console.log('发送给接口的参数:', params)

    const response = await IntegralDataApi.getStatisticsData(params)

    if (response.data) {
      tableData.value = response.data.list
      total.value = response.data.total
    }
    if (detailQueryParams.value.keyword) {
      tableData.value = tableData.value.filter((item) => {
        return item.name.includes(detailQueryParams.value.keyword)
      })
    }
  } catch (error) {
    console.error('获取明细数据失败:', error)
  }
}

// 处理明细页查询
const handleDetailQuery = () => {
  currentPage.value = 1
  // 根据接口参数要求调整参数
  const params = {
    deptId: detailQueryParams.value.departmentName || undefined,
    postId: detailQueryParams.value.position || undefined,
    roleId: undefined, // 暂时不传角色参数
    employDaysGreaterThan: detailQueryParams.value.joinDays || undefined,
    keyword: detailQueryParams.value.keyword || undefined, // 添加姓名过滤
    pageNo: currentPage.value,
    pageSize: pageSize.value
  }

  // 移除空值参数
  Object.keys(params).forEach((key) => {
    if (params[key] === undefined || params[key] === null || params[key] === '') {
      delete params[key]
    }
  })

  console.log('明细页查询参数:', params)
  getDetailData()
}

// 处理明细页重置
const handleDetailReset = () => {
  detailDateRange.value = undefined
  detailQueryParams.value = {
    startTime: '',
    endTime: '',
    departmentName: undefined as unknown as number,
    position: undefined as unknown as number,
    joinTime: 'gte',
    joinDays: '',
    keyword: ''
  }
  currentPage.value = 1
  getDetailData()
}

// 明细页数据
const detailDepartment = ref('')
const detailPosition = ref('')
const detailRank = ref('')
const detailUserGroup = ref('')
const detailJoinTime = ref('gte')
const detailJoinDays = ref('')
const searchType = ref('name')
const detailKeyword = ref('')

const viewDetail = (row: any) => {
  console.log('查看明细', row)
}

// 定义响应数据的状态
const rewardCountData = ref(null)
const hotExchangeProductsData = ref(null)
const pointsDistributionData = ref({
  totalDistribution: undefined,
  learningReward: undefined,
  interactiveReward: undefined,
  activeReward: undefined,
  teachingReward: undefined,
  manualRelease: undefined,
  rewardCount: undefined,
  coverageCount: undefined,
  coverageRate: undefined
})
const pointsConsumptionData = ref({
  totalConsumption: undefined,
  exchangeProductCount: undefined,
  coverageCount: undefined,
  coverageRate: undefined,
  totalPoints: undefined,
  averagePoints: undefined
})
const pointsBalanceData = ref({
  totalPoints: undefined,
  averagePoints: undefined
})

// 在组件挂载时调用接口
onMounted(async () => {
  try {
    // 先获取明细数据
    await getDetailData()

    // 获取其他统计数据
    const rewardCountResponse = await IntegralDataApi.getPointsRewardCount({}, headers)
    const rewardCountData = rewardCountResponse.data

    const rewardDistributionResponse = await IntegralDataApi.getPointsRewardDistribution(
      {},
      headers
    )
    const rewardDistributionData = rewardDistributionResponse.data

    const pointsDistributionResponse = await IntegralDataApi.getPointsDistribution()
    pointsDistributionData.value = pointsDistributionResponse.data
    console.log('积分发放数据:', pointsDistributionData.value)

    const pointsConsumptionResponse = await IntegralDataApi.getPointsConsumption()
    pointsConsumptionData.value = pointsConsumptionResponse.data
    console.log('积分消耗数据:', pointsConsumptionData.value)

    const pointsBalanceResponse = await IntegralDataApi.getPointsBalance()
    pointsBalanceData.value = pointsBalanceResponse.data
    console.log('积分余额数据:', pointsBalanceData.value)

    const hotExchangeProductsResponse = await IntegralDataApi.getHotExchangeProducts({}, headers)
    const hotExchangeProductsData = hotExchangeProductsResponse.data

    initRewardCountChart(rewardCountData)
    initRewardDistributionChart(rewardDistributionData)
    initProductsChart(hotExchangeProductsData)
  } catch (error) {
    console.error('获取数据失败:', error)
  }
})

onUnmounted(() => {
  rewardCountChart?.dispose()
  rewardDistributionChart?.dispose()
  productsChart?.dispose()
})
</script>

<style lang="scss" scoped>
.integral-data {
  min-height: 100vh;

  .overview-container {
    padding: 20px;
    background-color: #f5f5f5;
  }
  .section-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  :deep(.el-tabs--border-card) {
    background: #f5f5f5;
    border: none;
    box-shadow: none;

    > .el-tabs__header {
      background: #fff;
      border-bottom: 1px solid #dcdfe6;
      margin: 0;
    }

    > .el-tabs__content {
      background: transparent;
      padding: 0;
    }
  }

  .white-block {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .search-form {
    .el-form {
      padding: 20px;
      background: #fafafa;
    }
  }

  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      background: #fff;
      border-radius: 4px;
      padding: 20px;
      height: 100%;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .title {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
        font-weight: 500;
      }

      // 积分余额样式
      .balance-content {
        .balance-row {
          display: flex;
          justify-content: flex-start;
          gap: 60px;

          .balance-item {
            .number {
              font-size: 28px;
              color: #333;
              margin-bottom: 8px;
              font-weight: 500;
            }
            .label {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }

      // 积分消耗样式
      .consume-content {
        .consume-row {
          display: flex;
          justify-content: space-between;

          .consume-item {
            .number {
              font-size: 28px;
              color: #333;
              margin-bottom: 8px;
              font-weight: 500;
            }
            .label {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }
    }
  }

  .release-statistics {
    .title {
      font-size: 16px;
      color: #333;
      margin-bottom: 20px;
      font-weight: 500;
    }

    .stats-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .stat-item {
        text-align: center;
        flex: 1;

        .number {
          font-size: 24px;
          color: #333;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .charts-container {
    display: flex;
    gap: 20px;

    .chart-block {
      flex: 1;

      .title {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
      }
    }
  }

  .hot-products {
    .title {
      font-size: 16px;
      color: #333;
      margin-bottom: 20px;
    }
  }

  .detail-container {
    .table-container {
      .pagination {
        margin-top: 20px;
        text-align: center;
      }
    }
  }
}
</style>
