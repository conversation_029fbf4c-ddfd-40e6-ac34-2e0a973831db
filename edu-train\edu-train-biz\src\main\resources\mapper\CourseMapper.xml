<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.train.dao.course.CourseMapper">
    <select id="getCoursePage" resultType="cn.iocoder.yudao.module.train.model.course.resp.CoursePageResp"
            parameterType="cn.iocoder.yudao.module.train.model.course.req.CoursePageReq">
        SELECT
        tc.id,
        tc.title,
        tc.department_id as departmentId,
        td.name as departmentName,
        tc.status,
        tc.serial_status as serialStatus,
        tc.cover_image as coverImage,
        tc.create_time as createTime,
        tc.course_hours as courseHours,
        tc.responsible_person_id as responsiblePersonId,
        tc.category_id as categoryId,
        tc.course_type as courseType,
        tc.creator as creator,
        tc.sort as sort,
        tc.recommend as recommend,
        tc.updater as updater,
        tc.recommend_time as recommendTime,
        tc.tags as tags,
        tc.file_ids as fileIds,
        tc.start_time as startTime,
        tc.end_time as endTime,
        tc.location as location
        FROM
        train_course tc
        LEFT JOIN
        system_users su ON tc.creator = su.id
        LEFT JOIN
        system_dept td ON tc.department_id = td.id
        <where>
            tc.deleted = 0
            <if test="pageVO.title != null and pageVO.title != ''">
                AND tc.title LIKE CONCAT('%', #{pageVO.title}, '%')
            </if>
            <if test="pageVO.creatorName != null and pageVO.creatorName != ''">
                AND su.nickname LIKE CONCAT('%', #{pageVO.creatorName}, '%')
            </if>
            <if test="pageVO.creatorUserName != null and pageVO.creatorUserName != ''">
                AND su.username LIKE CONCAT('%', #{pageVO.creatorUserName}, '%')
            </if>
            <if test="pageVO.creatorMobile != null and pageVO.creatorMobile != ''">
                AND su.mobile LIKE CONCAT('%', #{pageVO.creatorMobile}, '%')
            </if>
            <if test="pageVO.status != null">
                AND tc.status = #{pageVO.status}
            </if>
            <if test="pageVO.categoryId != null">
                AND tc.category_id = #{pageVO.categoryId}
            </if>
            <if test="pageVO.departmentIds != null and pageVO.departmentIds.size() > 0">
                AND tc.department_id IN
                <foreach collection="pageVO.departmentIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="pageVO.departmentId != null and (pageVO.departmentIds == null or pageVO.departmentIds.size() == 0)">
                AND tc.department_id = #{pageVO.departmentId}
            </if>
            <if test="pageVO.courseType != null">
                AND tc.course_type = #{pageVO.courseType}
            </if>
            <if test="pageVO.tenantId != null">
                AND tc.tenant_id = #{pageVO.tenantId}
            </if>
            <if test="pageVO.recommend != null">
                AND tc.recommend = #{pageVO.recommend}
            </if>
            <if test="pageVO.tags != null and pageVO.tags != ''">
                AND tc.tags LIKE CONCAT('%', #{pageVO.tags}, '%')
            </if>
        </where>
        ORDER BY tc.create_time DESC
    </select>
    <select id="findStaffOnlineCourseRespPage"
            resultType="cn.iocoder.yudao.module.train.model.data.resp.StaffOnlineCourseResp">

        select DISTINCT
        tc.id as courseId,
        tc.title as courseName,
        tcc.id as categoryId,
        tcc.name as categoryName,
        tc.cover_image as coverImage,
        tc.create_time as createTime,
        tc.course_type as courseType,
        tc.recommend as recommend
        from train_course as tc
        left join train_course_category as tcc on tc.category_id = tcc.id
        left join system_users as su on tc.creator = su.id
        left join system_user_post as sup on sup.user_id = su.id
        left join system_dept as sd on sd.id = su.dept_id
        left join system_post as sp on sup.post_id = sp.id
        <where>
            tc.deleted = 0
            AND tc.course_type = 1
            <if test="pageReq.deptId != null and pageReq.deptId != ''">
                AND tc.department_id = #{pageReq.deptId}
            </if>
            <if test="pageReq.postId != null and pageReq.postId != ''">
                AND sp.id = #{pageReq.postId}
            </if>
            <if test="pageReq.categoryId != null and pageReq.categoryId != ''">
                AND tc.category_id = #{pageReq.categoryId}
            </if>
            <if test="pageReq.beginTime != null">
                AND tc.create_time &gt;= DATE(#{pageReq.beginTime})
            </if>
            <if test="pageReq.endTime != null">
                AND tc.create_time &lt;= DATE(#{pageReq.endTime }) + INTERVAL 1 DAY
            </if>
            <if test="pageReq.courseType != null and pageReq.courseType != ''">
                AND tc.course_type = #{pageReq.courseType}
            </if>
        </where>
        order by tc.create_time desc
    </select>


    <select id="findStaffOnlineCourseDetailRespPage"
            resultType="cn.iocoder.yudao.module.train.model.data.resp.StaffOnlineCourseDetailResp">

        select DISTINCT
        tc.id as courseId,
        tc.title as courseName,
        su.username as username,
        su.nickname as nickname,
        sd.id as deptId,
        sd.name as deptName,
        tcc.id as categoryId,
        tcc.name as categoryName,
        tc.create_time as createTime,
        tc.course_type as courseType,
        tc.recommend as recommend,
        tcm.join_time as joinTime
        from system_users as su
        left join train_course as tc on tc.creator = su.id
        left join train_course_category as tcc on tc.category_id = tcc.id
        left join train_course_management as tcm on tc.id = tcm.course_id and tcm.user_type = 2 and tcm.user_id = su.id
        left join system_user_post as sup on sup.user_id = su.id
        left join system_dept as sd on sd.id = su.dept_id
        left join system_post as sp on sup.post_id = sp.id
        <where>
            tc.deleted = 0
            AND tc.course_type = 1
            <if test="pageReq.courseId != null">
                AND tc.id = #{pageReq.courseId}
            </if>
            <if test="pageReq.deptId != null and pageReq.deptId != ''">
                AND tc.department_id = #{pageReq.deptId}
            </if>
            <if test="pageReq.postId != null and pageReq.postId != ''">
                AND sp.id = #{pageReq.postId}
            </if>
            <if test="pageReq.categoryId != null and pageReq.categoryId != ''">
                AND tc.category_id = #{pageReq.categoryId}
            </if>
            <if test="pageReq.beginTime != null">
                AND tc.create_time &gt;= DATE(#{pageReq.beginTime})
            </if>
            <if test="pageReq.endTime != null">
                AND tc.create_time &lt;= DATE(#{pageReq.endTime }) + INTERVAL 1 DAY
            </if>
            <if test="pageReq.courseType != null and pageReq.courseType != ''">
                AND tc.course_type = #{pageReq.courseType}
            </if>
            <if test="pageReq.courseName != null and pageReq.courseName != ''">
                AND tc.title LIKE CONCAT('%', #{pageReq.courseName}, '%')
            </if>
            <if test="pageReq.userId != null">
                AND su.id = #{pageReq.userId}
            </if>
            <if test="pageReq.keyword != null and pageReq.keyword != ''">
                AND (su.username LIKE CONCAT('%', #{pageReq.keyword}, '%') OR su.nickname LIKE CONCAT('%', #{pageReq.keyword}, '%'))
            </if>
            <if test="pageReq.nickName != null and pageReq.nickName != ''">
                AND su.nickname LIKE CONCAT('%', #{pageReq.nickName}, '%')
            </if>
            <if test="pageReq.userName != null and pageReq.userName != ''">
                AND su.username LIKE CONCAT('%', #{pageReq.userName}, '%')
            </if>
            <if test="pageReq.name != null and pageReq.name != '' and pageReq.nickName == null and pageReq.userName == null">
                AND su.nickname LIKE CONCAT('%', #{pageReq.name}, '%')
            </if>
        </where>
        order by tc.create_time desc

    </select>

    <select id="findStaffOfflineCourseRespPage"
            resultType="cn.iocoder.yudao.module.train.model.data.resp.StaffOfflineCourseResp">
        select DISTINCT
        su.username as username,
        su.nickname as nickName,
        sd.id as deptId,
        sd.name as deptName,
        tc.id as courseId,
        tc.title as courseName,
        tc.course_hours as courseHour,
        tc.recommend as recommend,
        tc.create_time as createTime
        from system_users as su
        left join train_course as tc on tc.creator = su.id
        left join train_course_category as tcc on tc.category_id = tcc.id
        left join system_user_post as sup on sup.user_id = su.id
        left join system_dept as sd on sd.id = su.dept_id
        left join system_post as sp on sup.post_id = sp.id
        <where>
            tc.deleted = 0
            <if test="pageReq.deptId != null and pageReq.deptId != ''">
                AND tc.department_id = #{pageReq.deptId}
            </if>
            <if test="pageReq.postId != null and pageReq.postId != ''">
                AND sp.id = #{pageReq.postId}
            </if>
            <if test="pageReq.categoryId != null and pageReq.categoryId != ''">
                AND tc.category_id = #{pageReq.categoryId}
            </if>
            <if test="pageReq.beginTime != null">
                AND tc.create_time &gt;= DATE(#{pageReq.beginTime})
            </if>
            <if test="pageReq.endTime != null">
                AND tc.create_time &lt;= DATE(#{pageReq.endTime }) + INTERVAL 1 DAY
            </if>
            <if test="pageReq.courseType != null and pageReq.courseType != ''">
                AND tc.course_type = #{pageReq.courseType}
            </if>
        </where>
        order by tc.create_time desc
    </select>

    <select id="findStaffOfflineClassRespPage"
            resultType="cn.iocoder.yudao.module.train.model.data.resp.StaffOfflineClassResp">
        select DISTINCT
        su.username as username,
        su.nickname as nickName,
        tc.create_time,
        sd.id as deptId,
        sd.name as deptName,
        tc.id as courseId,
        tc.title as courseName,
        tc.course_hours as courseHour,
        tc.recommend as recommend,
        tc.create_time as createTime
        from system_users as su
        left join train_course as tc on tc.creator = su.id
        left join train_course_category as tcc on tc.category_id = tcc.id
        left join system_user_post as sup on sup.user_id = su.id
        left join system_dept as sd on sd.id = su.dept_id
        left join system_post as sp on sup.post_id = sp.id
        <where>
            tc.deleted = 0
            <if test="pageReq.deptId != null and pageReq.deptId != ''">
                AND tc.department_id = #{pageReq.deptId}
            </if>
            <if test="pageReq.postId != null and pageReq.postId != ''">
                AND sp.id = #{pageReq.postId}
            </if>
            <if test="pageReq.categoryId != null and pageReq.categoryId != ''">
                AND tc.category_id = #{pageReq.categoryId}
            </if>
            <if test="pageReq.beginTime != null">
                AND tc.create_time &gt;= DATE(#{pageReq.beginTime})
            </if>
            <if test="pageReq.endTime != null">
                AND tc.create_time &lt;= DATE(#{pageReq.endTime }) + INTERVAL 1 DAY
            </if>
            <if test="pageReq.courseType != null and pageReq.courseType != ''">
                AND tc.course_type = #{pageReq.courseType}
            </if>
        </where>
        order by tc.create_time desc
    </select>
    <select id="selectCourseListWithoutPointConfig" resultType="cn.iocoder.yudao.module.train.api.course.model.CourseRespDTO">
        SELECT c.id, c.title, c.recommend
        FROM train_course c
                 LEFT JOIN cms_course_point_config p ON c.id = p.course_id AND p.deleted = 0
        WHERE p.course_id IS NULL
    </select>
    <select id="selectStaffKnowledgeBehaviorStatistics"
            resultType="cn.iocoder.yudao.module.train.controller.admin.data.vo.StaffKnowledgeBehaviorStatisticResp">
        SELECT
        u.id AS userId,
        u.nickname AS userName,
        SUM(CASE WHEN b.behavior = 'likes' THEN 1 ELSE 0 END) AS likeCount,
        SUM(CASE WHEN b.behavior = 'favorite' THEN 1 ELSE 0 END) AS favoriteCount,
        SUM(CASE WHEN b.behavior = 'subscribe' THEN 1 ELSE 0 END) AS subscribeCount
        FROM system_users u
        LEFT JOIN system_user_post up ON up.user_id = u.id
        LEFT JOIN cms_behavior_event b ON b.creator = u.id
        AND b.label = 4
        <if test="startTime != null and endTime != null">
            AND b.update_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <where>
            <if test="deptId != null">
                u.dept_id = #{deptId}
            </if>
            <if test="postId != null">
                AND up.post_id = #{postId}
            </if>
        </where>
        GROUP BY u.id, u.nickname
    </select>
    <select id="selectStaffTopicStatistics"
            resultType="cn.iocoder.yudao.module.train.controller.admin.data.vo.StaffTopicStatisticResp">
        SELECT
        u.id AS id,
        u.username AS userName,
        u.nickname AS nickName,
        -- 话题发布数
        (SELECT COUNT(1) FROM cms_topics t
        WHERE t.creator = u.id
        AND t.group_id IS NOT NULL
        <if test="startTime != null">AND t.update_time &gt;= #{startTime}</if>
        <if test="endTime != null">AND t.update_time &lt;= #{endTime}</if>
        ) AS topicCount,
        -- 话题回复数
        (SELECT COUNT(1) FROM cms_discussion d
        WHERE d.creator = u.id
        AND d.label = 7
        <if test="startTime != null">AND d.update_time &gt;= #{startTime}</if>
        <if test="endTime != null">AND d.update_time &lt;= #{endTime}</if>
        ) AS topicReplyCount,
        -- 资讯评论数
        (SELECT COUNT(1) FROM cms_discussion d
        WHERE d.creator = u.id
        AND d.label = 2
        <if test="startTime != null">AND d.update_time &gt;= #{startTime}</if>
        <if test="endTime != null">AND d.update_time &lt;= #{endTime}</if>
        ) AS infoReplyCount
        FROM system_users u
        <where>
            <if test="userIds != null and userIds.size() > 0">
                u.id IN
                <foreach collection="userIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCourseReferences" resultType="cn.iocoder.yudao.module.train.model.course.resp.CourseReferenceResp">
        SELECT
            t.id,
            t.project_id AS projectId,
            p.project_name AS projectName,
            t.create_time AS createTime,
            t.creator,
            u.nickname AS creatorName
        FROM
            train_project_task t
        LEFT JOIN
            train_project p ON t.project_id = p.id
        LEFT JOIN
            system_users u ON t.creator = u.id
        WHERE
            t.task_category_id = 1
            AND t.correlation_id = #{courseId}
    </select>

    <select id="getCourseTopicReferences" resultType="cn.iocoder.yudao.module.train.model.course.resp.CourseReferenceResp">
        SELECT
            r.id,
            r.topic_id AS projectId,
            c.title AS projectName,
            r.create_time AS createTime,
            r.creator,
            u.nickname AS creatorName
        FROM
            train_online_course_topic_relation r
        LEFT JOIN
            train_course c ON r.topic_id = c.id
        LEFT JOIN
            system_users u ON r.creator = u.id
        WHERE
            r.course_id = #{courseId}
    </select>

    <select id="batchGetCourseTopicReferenceCount" resultType="java.util.Map">
        SELECT
            r.course_id AS courseId,
            COUNT(1) AS count
        FROM
            train_online_course_topic_relation r
        WHERE
            r.course_id IN
            <foreach collection="courseIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY
            r.course_id
    </select>

    <select id="batchGetCourseProjectReferenceCount" resultType="java.util.Map">
        SELECT
            t.correlation_id AS courseId,
            COUNT(1) AS count
        FROM
            train_project_task t
        WHERE
            t.task_category_id = 1
            AND t.correlation_id IN
            <foreach collection="courseIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY
            t.correlation_id
    </select>
</mapper>
